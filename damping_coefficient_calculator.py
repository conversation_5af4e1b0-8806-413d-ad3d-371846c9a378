#!/usr/bin/env python3
"""
通用阻尼系数计算器
根据物体的物理参数自动计算合适的阻尼系数
"""

import numpy as np
import json

class DampingCoefficientCalculator:
    """阻尼系数计算器类"""
    
    def __init__(self, length, width, height, mass, diagonal_inertia, fluid_density=1000.0):
        """
        初始化计算器
        
        Args:
            length: 物体长度 (m)
            width: 物体宽度 (m)  
            height: 物体高度 (m)
            mass: 物体质量 (kg)
            diagonal_inertia: 对角惯性矩 [Ixx, Iyy, Izz] (kg·m²)
            fluid_density: 流体密度 (kg/m³), 默认1000为水
        """
        self.length = length
        self.width = width
        self.height = height
        self.mass = mass
        self.diagonal_inertia = np.array(diagonal_inertia)
        self.fluid_density = fluid_density
        
        # 计算基本物理量
        self.volume = length * width * height
        self.density = mass / self.volume
        self.surface_area = 2 * (length*width + length*height + width*height)
        self.characteristic_length = (self.volume) ** (1/3)
        
        # 计算不同方向的迎风面积
        self.frontal_areas = {
            'x': width * height,    # 沿X轴运动时的迎风面积
            'y': length * height,   # 沿Y轴运动时的迎风面积
            'z': length * width     # 沿Z轴运动时的迎风面积
        }
        self.avg_frontal_area = np.mean(list(self.frontal_areas.values()))
        
        # 计算力量级
        self.gravity_force = mass * 9.81
        self.max_buoyancy_force = self.volume * fluid_density * 9.81
        self.net_buoyancy = self.max_buoyancy_force - self.gravity_force
        
    def calculate_linear_drag_coefficients(self, target_terminal_velocity=1.5, use_empirical_tuning=True):
        """
        计算线性阻尼系数

        Args:
            target_terminal_velocity: 目标终端速度 (m/s)
            use_empirical_tuning: 是否使用经验调优（基于实际测试的有效参数）

        Returns:
            dict: 线性和二次阻尼系数
        """
        if use_empirical_tuning:
            # 🎯 基于实际有效参数的经验调优
            # 参考你的物体参数和已验证的有效系数

            # 计算缩放因子（相对于参考物体）
            reference_volume = 2.0 * 1.0 * 0.5  # 参考物体体积
            reference_mass = 500.0               # 参考物体质量
            reference_avg_frontal_area = (0.5 + 1.0 + 2.0) / 3  # 参考迎风面积

            volume_scale = self.volume / reference_volume
            mass_scale = self.mass / reference_mass
            area_scale = self.avg_frontal_area / reference_avg_frontal_area

            # 🔧 针对小型物体优化阻尼参数
            if self.mass < 10.0:  # 小型物体（<10kg）
                print(f"🔧 检测到小型物体 (质量: {self.mass}kg), 使用优化的阻尼计算")

                # 小型物体的阻尼参数 - 更强的阻尼以减少震荡
                # 基于临界阻尼理论：c = 2*sqrt(k*m)，其中k可以从浮力估算
                buoyancy_stiffness = abs(self.net_buoyancy) / 0.1  # 假设0.1m位移产生净浮力
                critical_damping_base = 2.0 * np.sqrt(buoyancy_stiffness * self.mass)

                # 线性阻尼：提供基础阻尼，防止低速震荡
                linear_coeff = max(
                    critical_damping_base * 1.5,  # 1.5倍临界阻尼（过阻尼）
                    self.mass * 10.0  # 基于质量的最小阻尼
                )

                # 二次阻尼：在高速时起作用，但不要过大
                quadratic_coeff = max(
                    self.avg_frontal_area * self.fluid_density * 1.2,  # 基于阻力公式
                    self.mass * 5.0  # 基于质量的最小值
                )

                print(f"  📊 临界阻尼基础值: {critical_damping_base:.1f}")
                print(f"  📊 优化后线性阻尼: {linear_coeff:.1f}")
                print(f"  📊 优化后二次阻尼: {quadratic_coeff:.1f}")

            else:
                # 大型物体使用原来的缩放方法
                # 已验证的有效参数（你的物体）
                reference_linear_coeff = 350.0
                reference_quadratic_coeff = 1800.0

                # 根据物理量缩放
                linear_coeff = reference_linear_coeff * (mass_scale ** 0.5) * (area_scale ** 0.5)
                quadratic_coeff = reference_quadratic_coeff * area_scale

                # 根据目标终端速度微调
                velocity_scale = target_terminal_velocity / 1.56  # 参考终端速度
                if velocity_scale != 1.0:
                    # 调整系数以达到目标终端速度
                    linear_coeff = linear_coeff / velocity_scale
                    quadratic_coeff = quadratic_coeff / (velocity_scale ** 2)

        else:
            # 原始理论计算方法
            linear_coeff_base = self.surface_area * 50
            drag_coefficient = 1.05
            quadratic_coeff_theoretical = 0.5 * self.fluid_density * drag_coefficient * self.avg_frontal_area

            if self.net_buoyancy > 0:
                linear_contribution = 0.3 * self.net_buoyancy
                quadratic_contribution = 0.7 * self.net_buoyancy

                linear_coeff = linear_contribution / target_terminal_velocity
                quadratic_coeff = quadratic_contribution / (target_terminal_velocity ** 2)

                linear_coeff = max(linear_coeff, linear_coeff_base * 0.5)
                quadratic_coeff = max(quadratic_coeff, quadratic_coeff_theoretical)
            else:
                linear_coeff = linear_coeff_base
                quadratic_coeff = quadratic_coeff_theoretical * 3

        return {
            'linear_drag_coefficient': round(linear_coeff, 1),
            'quadratic_drag_coefficient': round(quadratic_coeff, 1)
        }
    
    def calculate_angular_drag_coefficients(self, stability_enhancement=True, use_empirical_tuning=True):
        """
        计算角阻尼系数

        Args:
            stability_enhancement: 是否对俯仰和横滚施加额外阻尼
            use_empirical_tuning: 是否使用经验调优

        Returns:
            dict: 角阻尼系数和稳定化参数
        """
        if use_empirical_tuning:
            # 🎯 基于实际有效参数的经验调优
            # 参考你的物体参数和已验证的有效系数

            # 参考物体的惯性矩
            reference_inertia = [104.17, 354.17, 416.67]
            reference_avg_inertia = np.mean(reference_inertia)

            # 已验证的有效角阻尼参数
            reference_angular_linear = 600.0
            reference_angular_quadratic = 1000.0

            # 计算惯性矩缩放因子
            current_avg_inertia = np.mean(self.diagonal_inertia)
            inertia_scale = current_avg_inertia / reference_avg_inertia

            # 🔧 修复：为小型物体设置最小角阻尼值
            # 根据惯性矩缩放角阻尼系数，但设置合理的最小值
            angular_linear_coeff = max(
                reference_angular_linear * inertia_scale,
                0.1  # 最小角线性阻尼 0.1 N·m·s/rad
            )
            angular_quadratic_coeff = max(
                reference_angular_quadratic * inertia_scale,
                0.2  # 最小角二次阻尼 0.2 N·m·s²/rad²
            )

            # 🎯 对于非常小的物体，使用基于尺寸的角阻尼估算
            if current_avg_inertia < 0.1:  # 小型物体
                print(f"⚠️  检测到小型物体 (惯性矩: {current_avg_inertia:.4f}), 使用尺寸基础的角阻尼计算")

                # 基于物体尺寸和流体密度的角阻尼估算
                characteristic_moment = self.fluid_density * (self.characteristic_length ** 5)

                # 为小型无人机提供更合适的角阻尼值
                # 参考：小型无人机在水中的典型角阻尼范围
                size_based_angular_linear = max(0.5, characteristic_moment * 0.1)
                size_based_angular_quadratic = max(1.0, characteristic_moment * 0.2)

                angular_linear_coeff = max(angular_linear_coeff, size_based_angular_linear)
                angular_quadratic_coeff = max(angular_quadratic_coeff, size_based_angular_quadratic)

                print(f"  📊 尺寸基础角阻尼: 线性={size_based_angular_linear:.1f}, 二次={size_based_angular_quadratic:.1f}")

        else:
            # 原始理论计算方法
            avg_inertia = np.mean(self.diagonal_inertia)
            angular_linear_coeff = avg_inertia * 2.0
            angular_quadratic_coeff = avg_inertia * 3.0

        # 稳定化因子
        if stability_enhancement:
            # 对俯仰(X)和横滚(Y)施加额外阻尼，保持偏航(Z)正常
            stability_factor = [3.0, 3.0, 1.0]
        else:
            stability_factor = [1.0, 1.0, 1.0]

        return {
            'angular_drag_coefficient': round(angular_linear_coeff, 1),
            'angular_quadratic_drag_coefficient': round(angular_quadratic_coeff, 1),
            'stability_factor': stability_factor
        }
    
    def generate_config(self, target_terminal_velocity=1.5, stability_enhancement=True, use_empirical_tuning=True):
        """
        生成完整的配置字典

        Args:
            target_terminal_velocity: 目标终端速度 (m/s)
            stability_enhancement: 是否启用姿态稳定增强
            use_empirical_tuning: 是否使用经验调优（推荐True）

        Returns:
            dict: 完整的配置字典
        """
        linear_coeffs = self.calculate_linear_drag_coefficients(target_terminal_velocity, use_empirical_tuning)
        angular_coeffs = self.calculate_angular_drag_coefficients(stability_enhancement, use_empirical_tuning)
        
        config = {
            # 物体参数
            'object_dimensions': [self.length, self.width, self.height],
            'object_mass': self.mass,
            'object_diagonal_inertia': self.diagonal_inertia.tolist(),
            'object_volume': round(self.volume, 3),
            'object_density': round(self.density, 1),
            
            # 流体参数
            'fluid_density': self.fluid_density,
            
            # 线性阻尼系数
            'linear_drag_coefficient': linear_coeffs['linear_drag_coefficient'],
            'quadratic_drag_coefficient': linear_coeffs['quadratic_drag_coefficient'],
            
            # 角阻尼系数
            'angular_drag_coefficient': angular_coeffs['angular_drag_coefficient'],
            'angular_quadratic_drag_coefficient': angular_coeffs['angular_quadratic_drag_coefficient'],
            
            # 稳定化参数
            'stability_factor': angular_coeffs['stability_factor'],
            
            # 预测性能
            'predicted_terminal_velocity': target_terminal_velocity,
            'net_buoyancy_force': round(self.net_buoyancy, 1),
            'gravity_force': round(self.gravity_force, 1),
            'max_buoyancy_force': round(self.max_buoyancy_force, 1),
        }
        
        return config
    
    def print_analysis(self, config):
        """打印详细分析"""
        print("=" * 80)
        print("阻尼系数计算结果")
        print("=" * 80)
        
        print(f"\n📐 物体参数:")
        print(f"  尺寸: {self.length}m × {self.width}m × {self.height}m")
        print(f"  质量: {self.mass} kg")
        print(f"  体积: {config['object_volume']} m³")
        print(f"  密度: {config['object_density']} kg/m³")
        print(f"  对角惯性矩: {config['object_diagonal_inertia']} kg·m²")
        
        print(f"\n🌊 流体力学参数:")
        print(f"  表面积: {self.surface_area:.2f} m²")
        print(f"  平均迎风面积: {self.avg_frontal_area:.2f} m²")
        print(f"  特征长度: {self.characteristic_length:.2f} m")
        
        print(f"\n⚖️ 力量级分析:")
        print(f"  重力: {config['gravity_force']} N")
        print(f"  最大浮力: {config['max_buoyancy_force']} N")
        print(f"  净浮力: {config['net_buoyancy_force']} N")
        
        print(f"\n🎯 推荐阻尼系数:")
        print(f"  线性阻尼系数: {config['linear_drag_coefficient']} N·s/m")
        print(f"  二次阻尼系数: {config['quadratic_drag_coefficient']} N·s²/m²")
        print(f"  角线性阻尼系数: {config['angular_drag_coefficient']} N·m·s/rad")
        print(f"  角二次阻尼系数: {config['angular_quadratic_drag_coefficient']} N·m·s²/rad²")
        print(f"  稳定化因子: {config['stability_factor']}")
        
        print(f"\n🚀 预期性能:")
        print(f"  目标终端速度: {config['predicted_terminal_velocity']} m/s")
        
        # 计算不同速度下的阻尼力
        test_velocities = [0.5, 1.0, 1.5, 2.0]
        print(f"\n📊 不同速度下的阻尼力:")
        print("  速度(m/s) | 阻尼力(N) | 阻尼/净浮力比")
        print("  " + "-" * 40)
        for v in test_velocities:
            drag_force = config['linear_drag_coefficient'] * v + config['quadratic_drag_coefficient'] * v * v
            ratio = drag_force / abs(config['net_buoyancy_force']) if config['net_buoyancy_force'] != 0 else 0
            print(f"  {v:8.1f} | {drag_force:8.0f} | {ratio:12.2f}")

def create_config_for_object(length, width, height, mass, diagonal_inertia, 
                           target_terminal_velocity=1.5, stability_enhancement=True,
                           fluid_density=1000.0):
    """
    便捷函数：为指定物体创建配置
    
    Args:
        length, width, height: 物体尺寸 (m)
        mass: 质量 (kg)
        diagonal_inertia: 对角惯性矩 [Ixx, Iyy, Izz] (kg·m²)
        target_terminal_velocity: 目标终端速度 (m/s)
        stability_enhancement: 是否启用姿态稳定增强
        fluid_density: 流体密度 (kg/m³)
    
    Returns:
        dict: 配置字典
    """
    calculator = DampingCoefficientCalculator(
        length, width, height, mass, diagonal_inertia, fluid_density
    )
    
    config = calculator.generate_config(target_terminal_velocity, stability_enhancement)
    calculator.print_analysis(config)
    
    return config

def get_damping_config_dict(length, width, height, mass, diagonal_inertia,
                          target_terminal_velocity=1.5, stability_enhancement=True, use_empirical_tuning=True):
    """
    简化接口：直接返回可用于 self.config 的字典

    Args:
        length, width, height: 物体尺寸 (m)
        mass: 质量 (kg)
        diagonal_inertia: 对角惯性矩 [Ixx, Iyy, Izz] (kg·m²)
        target_terminal_velocity: 目标终端速度 (m/s)
        stability_enhancement: 是否启用姿态稳定增强
        use_empirical_tuning: 是否使用经验调优（推荐True，基于实际有效参数）

    Returns:
        dict: 只包含配置参数的字典
    """
    calculator = DampingCoefficientCalculator(
        length, width, height, mass, diagonal_inertia
    )

    full_config = calculator.generate_config(target_terminal_velocity, stability_enhancement, use_empirical_tuning)

    # 只返回配置相关的参数
    config_dict = {
        'fluid_density': full_config['fluid_density'],
        'linear_drag_coefficient': full_config['linear_drag_coefficient'],
        'quadratic_drag_coefficient': full_config['quadratic_drag_coefficient'],
        'angular_drag_coefficient': full_config['angular_drag_coefficient'],
        'angular_quadratic_drag_coefficient': full_config['angular_quadratic_drag_coefficient'],
        'stability_factor': full_config['stability_factor'],
    }

    return config_dict

if __name__ == "__main__":
    # 使用你的物体参数进行测试
    print("使用你的物体参数计算阻尼系数:")
    config = create_config_for_object(
        length=2.0,
        width=1.0,
        height=0.5,
        mass=500.0,
        diagonal_inertia=[104.17, 354.17, 416.67],
        target_terminal_velocity=1.5,
        stability_enhancement=True
    )

    print(f"\n💾 生成的配置 (可直接用于 self.config):")
    print("=" * 50)
    for key, value in config.items():
        if key.startswith('object_') or key.startswith('predicted_') or key.startswith('net_') or key.startswith('gravity_') or key.startswith('max_'):
            continue  # 跳过分析参数，只显示配置参数
        print(f"'{key}': {value},")

    print(f"\n🔧 简化使用示例:")
    print("=" * 50)
    print("# 在你的代码中使用:")
    print("from damping_coefficient_calculator import get_damping_config_dict")
    print("")
    print("damping_config = get_damping_config_dict(")
    print("    length=2.0, width=1.0, height=0.5,")
    print("    mass=500.0,")
    print("    diagonal_inertia=[104.17, 354.17, 416.67]")
    print(")")
    print("")
    print("# 然后更新你的 self.config:")
    print("self.config.update(damping_config)")
