
from isaacsim import SimulationApp

simulation_app = SimulationApp({"headless": False})

# 导入必要的库和模块
import omni
import carb.tokens                                      # Carbonite令牌系统
import numpy as np                                      # 数值计算库
import omni.kit.commands                               # Omniverse Kit命令系统
import omni.kit.test                                   # Omniverse Kit测试框架
from isaacsim.core.api import World                    # Isaac Sim世界管理器
from isaacsim.core.utils.prims import get_prim_at_path # 获取场景对象的工具函数
from isaacsim.core.utils.stage import create_new_stage # 创建新场景的工具函数
from isaacsim.core.utils.stage import open_stage      # 打开场景文件的工具函数
from isaacsim.core.api.world import World             # Isaac Sim世界管理器
from isaacsim.core.utils.stage import add_reference_to_stage
from isaacsim.core.api.robots import Robot
from isaacsim.core.prims import Articulation
from isaacsim.core.prims import RigidPrim
# 复合类
'''
class ROVSimulation:
        def __init__(self):
            self.world = None
            self.cube_prim = None
            self.cube_view = None
 
                # 配置参数（将在main中设置）
            self.config = {
                'water_density': 1.0,
                'gravity': 9.8,
                'object_mass': 0.4,
                'max_damping': 2.0,
                'max_buoyancy_force': 50.0,
                'max_thruster_force': 20.0,
                'max_controller_force': 10.0,  # 降低控制器力限制
                'max_total_force': 50.0,       # 降低总力限制
                'debug_mode': True,
                'water_surface_z': 0.0,        # 水面高度
                'reset_threshold_z': 100.0     # 重置阈值高度
            }

            # 调试控制
            self.debug_counter = 0
            self.debug_interval = 60  # 每60帧输出一次详细调试信息
        def apply_config(self, config_dict):
            """应用配置参数到仿真实例"""
            self.config.update(config_dict)

        def check_and_reset_if_needed(self, current_z):
            """检查物体位置，如果异常则重置"""
            # if abs(current_z) > self.config['reset_threshold_z']:
            #     print(f"⚠️ 物体位置异常 (Z={current_z:.1f}m)，执行重置...")
            #     self.reset_object_position()
            #     return True
            # return False
            pass 

        def setup_world(self):
            """设置仿真世界"""
            # 先不创建World对象，等USD加载后再创建
            pass
        
        def load_usd_scene(self, usd_path: str):
            """
            加载USD场景文件 - 直接打开方式

            Args:
                usd_path: USD文件的绝对路径
            """
            print("🌊 加载 USD 环境文件...")
            
            if not os.path.exists(usd_path):
                carb.log_error(f"❌ USD文件不存在: {usd_path}")
                return False

            try:
                abs_path = os.path.abspath(usd_path)
                print(f"📍 绝对路径: {abs_path}")
                
                # 关闭当前场景
                context = omni.usd.get_context()
                context.close_stage()
                
                # 直接打开USD文件
                print("🔄 直接打开USD文件作为主场景...")
                success = context.open_stage(abs_path)

                if success:
                    print("✅ USD文件已作为主场景打开")

                    # 等待几帧让USD完全加载
                    for _ in range(3):
                        simulation_app.update()

                    # 现在创建World对象（仍然需要它来管理RigidPrimView）
                    self.world = World(stage_units_in_meters=1.0)

                    # 🚨 关键修复：禁用Isaac Sim的自动重力，我们手动控制浮力和重力平衡
                    try:
                        # 获取物理场景并禁用重力
                        physics_context = self.world.get_physics_context()
                        if physics_context:
                            # 设置重力为0，我们通过浮力计算来手动控制重力效果
                            physics_context.set_gravity(0.0)
                            print("✅ 已禁用Isaac Sim自动重力，使用手动浮力控制")
                        else:
                            print("⚠️ 无法获取物理上下文，重力设置可能无效")
                    except Exception as e:
                        print(f"⚠️ 重力设置失败: {e}")

                    # 重置世界以初始化场景
                    self.world.reset()
                    
                    # 获取stage以供后续使用
                    self.stage = context.get_stage()
                    
                    print("🎯 World对象和Stage已准备就绪")
                    return True
                else:
                    carb.log_error("❌ 打开USD文件失败")
                    return False

            except Exception as e:
                carb.log_error(f"❌ 加载USD文件失败: {e}")
                import traceback
                traceback.print_exc()
                return False
'''



# 创建一个新的场景
environment_path = "/home/<USER>/Learn_standalone_isaac/usd_assets/ground_water.usd"
omni.usd.get_context().open_stage(environment_path)
# world里面包含了SimulationContext,进行物理仿真
if World.instance():
        World.instance().clear_instance()
world = World(stage_units_in_meters=1.0)    
world.initialize_physics()

# 3. 使用修改碰撞体积之后的机器人
robot_path = "/home/<USER>/Learn_standalone_isaac/usd_assets/iris_fov.usd"
add_reference_to_stage(usd_path=robot_path, prim_path="/World/iris")

world.scene.add_ground_plane(z_position=-1.0)

# 4. 创建机器人对象
# iris_robot = world.scene.add(Robot(prim_path="/World/iris", name="iris"))
# 变更为Articulation对象
iris_robot = world.scene.add(Articulation(prim_paths_expr="/World/iris", name="iris"))
robot_prim_path = "/World/iris"
body_names = iris_robot.body_names
body_prim_paths = [f"{robot_prim_path}/{name}" for name in body_names]
print(f"为 RigidPrimView 创建的刚体路径列表: {body_prim_paths}")

# 使用这些路径创建一个 RigidPrimView
iris_rigid_view = world.scene.add(
    RigidPrim(
        prim_paths_expr=body_prim_paths,
        name="iris_rigid_view"
    )
)

# 5. 重置世界
world.reset()

# 先运行若干步，让机器人自然衰减到较为静止的状态
for i in range(10):
    simulation_app.update()
# 开始模拟,获取到timeline，等于播放按钮
# omni.timeline.get_timeline_interface().play()

from pxr import UsdGeom,Gf
import math

parts = [
    "/World/iris/base_link/visuals",
    "/World/iris/base_link/collisions",
    "/World/iris/rotor_0/visuals",
    "/World/iris/rotor_1/visuals",
    "/World/iris/rotor_2/visuals",
    "/World/iris/rotor_3/visuals"
]



from utils_calculate.geometry_utils_volume import slice_convex_poly_volume
import isaacsim.core.utils.mesh as mesh_utils
import isaacsim.core.utils.stage as stage_utils
from isaacsim.core.prims import XFormPrim

prim_path = "/World/iris/base_link/collisions"
mesh_prim = stage_utils.get_current_stage().GetPrimAtPath(prim_path)

coord_prim_path="/World/world_point"
coord_prim = stage_utils.get_current_stage().GetPrimAtPath(coord_prim_path)

world_vertices = mesh_utils.get_mesh_vertices_relative_to(mesh_prim, coord_prim)
print(world_vertices)

total, below, above =  slice_convex_poly_volume(world_vertices, plane_normal=(0,0,1),plane_d=0)
print("water_total: ", total)
print("water_below: ", below)
print("water_above: ", above)

try:
    while simulation_app.is_running():
        world.step()
except KeyboardInterrupt:
    print("用户中断仿真")
except Exception as e:
    carb.log_error(f"仿真错误: {e}")
finally:
    simulation_app.close()
    
    