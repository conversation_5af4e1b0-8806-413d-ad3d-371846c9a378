#!/usr/bin/env python3
"""
无人机水下体积实时计算 - Standalone模式

此脚本演示了如何以结构化的方式在Isaac Sim中实时计算
一个复杂关节机器人（如无人机）特定部位浸入水中的体积。

- 目标环境: ground_water.usd
- 目标对象: /World/iris (无人机)
- 核心计算: 实时切分碰撞网格，计算水下部分的体积
"""

# Isaac Sim标准初始化代码
from isaacsim import SimulationApp
simulation_app = SimulationApp({"headless": False})

# 🔧 导入阻尼系数自动计算器
from damping_coefficient_calculator import get_damping_config_dict
from hydrodynamic_damping_system import create_hydrodynamic_damping_system, HydrodynamicDampingSystem

import os
import numpy as np
import carb
import omni.usd
from pxr import UsdGeom
from isaacsim.core.api import World                    # Isaac Sim世界管理器
from isaacsim.core.prims import Articulation,RigidPrim
from isaacsim.core.utils.stage import add_reference_to_stage
from isaacsim.core.utils.prims import get_prim_at_path # 获取场景对象的工具函数
import isaacsim.core.utils.mesh as mesh_utils
import isaacsim.core.utils.stage as stage_utils
from utils_calculate.geometry_utils_volume import slice_convex_poly_volume
from dataclasses import dataclass
from typing import Optional, Dict, Any, Tuple

@dataclass
class SimulationState:
    """仿真状态数据类"""
    world: Optional[Any] = None
    iris_robot: Optional[Any] = None
    target_collision_prim: Optional[Any] = None
    coord_prim: Optional[Any] = None
    robot_Articulation_prim: Optional[Any] = None
    rigid_link: Optional[Any] = None

@dataclass
class PhysicsData:
    """物理数据类"""
    gravity: Optional[float] = None
    gravity_magnitude: float = 9.81
    gravity_vector: np.ndarray = None
    water_density: float = 1000.0
    last_volume_data: Optional[Dict] = None
    buoyancy_force: Optional[float] = None
    world_vertices_record: Optional[np.ndarray] = None

    def __post_init__(self):
        if self.gravity_vector is None:
            self.gravity_vector = np.array([0.0, 0.0, -9.81])

@dataclass
class DampingConfig:
    """阻尼配置数据类"""
    target_terminal_velocity: float = 1.5
    stability_enhancement: bool = True
    use_empirical_tuning: bool = True
    use_hydrodynamic_damping: bool = True
    damping_config: Optional[Dict] = None
    hydrodynamic_damping_system: Optional[Any] = None

@dataclass
class SimulationConfig:
    """仿真配置数据类"""
    water_surface_z: float = 0
    water_plane_normal: tuple = (0, 0, 1)
    robot_prim_path: str = "/World/iris"
    collision_body_rel_path: str = "base_link/collisions"
    coord_prim_path: str = "/World/world_point"
    debug_mode: bool = True
    record_data_filename: str = "cog_simulation_data.npz"

    # 性能控制常量
    VOLUME_CALCULATION_THRESHOLD: float = 1e-6
    VELOCITY_THRESHOLD: float = 1e-6
    ANGULAR_VELOCITY_THRESHOLD: float = 1e-6

    # 性能优化参数
    VOLUME_CALC_FREQUENCY: int = 2  # 每N帧计算一次体积（降低计算频率）
    CACHE_VERTICES_FRAMES: int = 5  # 顶点缓存帧数
    MIN_POSITION_CHANGE: float = 0.001  # 最小位置变化阈值(m)

@dataclass
class PerformanceCache:
    """性能缓存数据类"""
    last_vertices_cache: Optional[np.ndarray] = None
    last_position_cache: Optional[np.ndarray] = None
    last_volume_cache: Optional[Dict] = None
    cache_valid_frames: int = 0

class IrisBuoyancySim:
    """
    无人机浮力仿真主类
    """
    def __init__(self):
        """初始化仿真环境状态 - 重构版本"""
        # 使用数据类组织相关数据
        self.state = SimulationState()
        self.physics = PhysicsData()
        self.damping = DampingConfig()
        self.config = SimulationConfig()
        self.cache = PerformanceCache()

        # 仿真控制变量
        self.time_flag = 1
        self.sim_frame_count = 0
        self.water_volume_fps = True

        # 数据记录
        self.data_log = {
            'sim_frame': [],
            'com_position': [],
            'linear_velocity': [],
            'quaternion_orientation': [],
        }

        # 兼容性变量（保持向后兼容）
        self.base_link_pose = None
    # 完成对于质量，惯性，尺寸的获取
    def _get_actual_physical_params(self):
        """
        从 USD 场景中获取实际的物理属性
        """
        from pxr import UsdPhysics, UsdGeom

        # 设置默认值，避免变量未定义错误
        default_mass = 1.0
        default_inertia = [1.0, 1.0, 1.0]
        default_scale = [1.0, 1.0, 1.0]

        # 初始化返回值
        actual_mass = default_mass
        actual_inertia = default_inertia
        actual_scale = default_scale

        cube_mesh_path = self.config.robot_prim_path + "/" + self.config.collision_body_rel_path
        cube_mesh_prim = stage_utils.get_current_stage().GetPrimAtPath(cube_mesh_path)

        if cube_mesh_prim.IsValid():
            print(f"✅ 找到 Cube Mesh: {cube_mesh_path}")

            # 获取质量
            if cube_mesh_prim.HasAPI(UsdPhysics.MassAPI):
                mass_api = UsdPhysics.MassAPI(cube_mesh_prim)
                if mass_api.GetMassAttr().HasValue():
                    actual_mass = float(mass_api.GetMassAttr().Get())
                    print(f"📊 实际 Mesh 质量: {actual_mass} kg")

                # 获取惯性
                if mass_api.GetDiagonalInertiaAttr().HasValue():
                    inertia_vec = mass_api.GetDiagonalInertiaAttr().Get()  # Gf.Vec3f
                    actual_inertia = [float(inertia_vec[0]), float(inertia_vec[1]), float(inertia_vec[2])]
                    print(f"📊 实际 Diagonal inertia: {actual_inertia}")
                else:
                    print("⚠️  没有设置惯量属性，使用默认值")
            else:
                print("⚠️  Mesh 没有 Mass API，使用默认质量")

            # 获取缩放
            cube_xformable = UsdGeom.Xformable(cube_mesh_prim)
            xform_ops = cube_xformable.GetOrderedXformOps()

            for op in xform_ops:
                if op.GetOpType() == UsdGeom.XformOp.TypeScale:
                    scale_vec = op.Get()
                    actual_scale = [float(scale_vec[0]), float(scale_vec[1]), float(scale_vec[2])]
                    print(f"📊 实际 Scale 缩放: {actual_scale}")
                    break
        else:
            print(f"❌ 无法找到 Cube Mesh: {cube_mesh_path}，使用默认参数")

        return {
            'mass': actual_mass,
            'diagonal_inertia': actual_inertia,
            'scale': actual_scale
        }
        
    def apply_config(self, config_dict):
        """应用外部配置参数到仿真实例"""
        # 更新数据类的属性
        for key, value in config_dict.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                print(f"✅ 更新配置: {key} = {value}")
            else:
                print(f"⚠️  未知配置项: {key}")
        print(f"✅ 配置已应用: {len(config_dict)} 个参数")

    def setup_scene(self, environment_usd_path: str, robot_usd_path: str):
        """设置仿真世界、加载环境和机器人"""
        print("🌊 开始设置场景...")

        # 1. 加载环境USD
        if not os.path.exists(environment_usd_path):
            carb.log_error(f"❌ 环境USD文件不存在: {environment_usd_path}")
            return False
        
        omni.usd.get_context().open_stage(environment_usd_path)
        # 更改渲染步长和物理步长
        # 2. 创建并初始化World对象
        self.state.world = World(
            physics_dt = 1.0 / 120.0,
            rendering_dt = 1.0 / 60.0,
            stage_units_in_meters=1.0)

        self.state.world.initialize_physics()
        
        # 3. 加载机器人USD
        if not os.path.exists(robot_usd_path):
            carb.log_error(f"❌ 机器人USD文件不存在: {robot_usd_path}")
            return False
        robot_path = self.config.robot_prim_path
        add_reference_to_stage( 
            usd_path=robot_usd_path, 
            prim_path=robot_path
        )
        
        # 查找全部prim組件
        # for prim in self.world.stage.Traverse():
        #     print("prim:",prim.GetPath())

        # 4. 创建机器人仿真对象（使用 Articulation）

        self.state.iris_robot = self.state.world.scene.add(
            Articulation(
                prim_paths_expr=robot_path,
                name="iris"
            )
        )
        print(f"✅ 刚体机器人 '{robot_path}' 已添加")

        self.state.robot_Articulation_prim = self.state.world.scene.get_object("iris")
        print(f"  - 机器人世界坐标: {self.state.robot_Articulation_prim.get_world_poses()}")

        # 5. 获取用于计算体积的目标碰撞体Prim
        collision_prim_path = f"{robot_path.rstrip('/')}/{self.config.collision_body_rel_path.lstrip('/')}"
        print("collision_prim_path:",collision_prim_path)
        # collision_prim_path: /World/iris/base_link/collisions

        # 获取原始 Prim
        collision_prim = stage_utils.get_current_stage().GetPrimAtPath(collision_prim_path)

        if not collision_prim.IsValid():
            carb.log_error(f"❌ 无法找到目标碰撞体: {collision_prim_path}")
            return False

        # 检查 prim 类型并转换为 UsdGeom.Mesh，一定需要处理mesh类型，否则无法使用顶点api
        if collision_prim.IsA(UsdGeom.Mesh):
            self.state.target_collision_prim = UsdGeom.Mesh(collision_prim)
            print(f"✅ 目标碰撞体已锁定为 Mesh: {collision_prim_path}")
        else:
            # 如果不是 Mesh，尝试查找子级中的 Mesh
            print(f"⚠️  Prim {collision_prim_path} 不是 Mesh 类型，尝试查找子级 Mesh...")
            mesh_found = False
            for child in collision_prim.GetAllChildren():
                if child.IsA(UsdGeom.Mesh):
                    self.state.target_collision_prim = UsdGeom.Mesh(child)
                    print(f"✅ 找到子级 Mesh: {child.GetPath()}")
                    mesh_found = True
                    break

            if not mesh_found:
                carb.log_error(f"❌ 在 {collision_prim_path} 及其子级中未找到有效的 Mesh")
                return False
        

        # 5.1 创建参考点
        self.state.coord_prim = stage_utils.get_current_stage().GetPrimAtPath(self.config.coord_prim_path)

        # 主体管理
        self.state.rigid_link = RigidPrim(prim_paths_expr="/World/iris/base_link", name="iris_body_link")

        # 6. 重置世界，让所有对象初始化
        self.state.world.reset()

        # 🎯 获取实际物理属性并计算阻尼系数
        actual_params = self._get_actual_physical_params()

        # 🚀 使用实际物理属性计算阻尼系数
        print("🔧 正在根据实际物体参数自动计算阻尼系数...")

        # 根据缩放调整尺寸
        actual_length = actual_params['scale'][0]
        actual_width = actual_params['scale'][1]
        actual_height = actual_params['scale'][2]

        self.damping_config = get_damping_config_dict(
            length=actual_length,
            width=actual_width,
            height=actual_height,
            mass=actual_params['mass'],
            diagonal_inertia=actual_params['diagonal_inertia'],
            target_terminal_velocity=self.damping.target_terminal_velocity,
            stability_enhancement=self.damping.stability_enhancement,
            use_empirical_tuning=self.damping.use_empirical_tuning
        )

        print("✅ 基于实际物理属性的阻尼系数计算完成:")
        print(f"  📊 使用质量: {actual_params['mass']} kg")
        print(f"  📊 使用惯性: {actual_params['diagonal_inertia']}")
        print(f"  📊 使用尺寸: L={actual_length:.2f}, W={actual_width:.2f}, H={actual_height:.2f}")
        print(f"  🔧 线性阻尼: {self.damping_config['linear_drag_coefficient']} N·s/m")
        print(f"  🔧 二次阻尼: {self.damping_config['quadratic_drag_coefficient']} N·s²/m²")
        print(f"  🔧 角线性阻尼: {self.damping_config['angular_drag_coefficient']} N·m·s/rad")
        print(f"  🔧 角二次阻尼: {self.damping_config['angular_quadratic_drag_coefficient']} N·m·s²/rad²")
        print(f"  🔧 稳定化因子: {self.damping_config['stability_factor']}")

        # 🚀 将计算出的阻尼系数添加到配置中
        self.damping.damping_config = self.damping_config

        # 🌊 初始化阻尼系统
        characteristic_length = max(actual_length, actual_width, actual_height)

        if self.damping.use_hydrodynamic_damping:
            print("\n🌊 初始化水动力学阻尼系统...")

            # 创建水动力学阻尼系统
            self.damping.hydrodynamic_damping_system = create_hydrodynamic_damping_system(
                mass=actual_params['mass'],
                inertia=actual_params['diagonal_inertia'],
                characteristic_length=characteristic_length
            )

            print(f"✅ 水动力学阻尼系统已初始化")
            print(f"  📊 特征长度: {characteristic_length:.3f} m")

            # 显示系统状态
            status = self.damping.hydrodynamic_damping_system.get_system_status()
            print(f"  📊 基础线性阻尼: {status['base_linear_damping']:.2f} N·s/m")
            print(f"  📊 基础二次阻尼: {status['base_quadratic_damping']:.2f} N·s²/m²")
            print(f"  📊 基础角阻尼: {status['base_angular_damping']:.2f} N·m·s/rad")
            print(f"  📊 速度分层: {len(status['velocity_zones'])} 个区间")

        else:
            print("⚠️  使用传统固定阻尼系数")

        # 7. 注册物理回调！
        self.state.world.add_physics_callback(
            "iris_volume_calculator",
            callback_fn=self._volume_calculation_callback
        )
        print("✅ 物理回调函数 'iris_volume_calculator' 已注册")

        # 8. 计算重力[返回一个元组，第一个元素对应重力方向向量，第二个元素是大小]
        self.physics.gravity_vector, self.physics.gravity_magnitude = self.state.world.get_physics_context().get_gravity()
        self.physics.gravity_vector = np.array(self.physics.gravity_vector)
        self.physics.gravity = self.physics.gravity_magnitude * sum(sum(self.state.iris_robot.get_body_masses()))
        print("mass:",sum(sum(self.state.iris_robot.get_body_masses())))
        print("gravity:",self.physics.gravity)
        print("gravity_vector:",self.physics.gravity_vector)

        # 运行几帧让物理稳定
        for _ in range(5):
            simulation_app.update()

        print("🎉 场景设置成功!")
        return True

    def _get_mesh_vertices_safe(self):
        """
        安全的获取网格顶点的备用方法，避免 MaterialBindingAPI 问题
        """
        try:
            # 获取本地顶点
            points_attr = self.state.target_collision_prim.GetPointsAttr()
            if not points_attr:
                raise ValueError("Mesh 没有顶点属性")

            local_points = points_attr.Get()
            if not local_points:
                raise ValueError("Mesh 顶点数据为空")

            # 转换为 numpy 数组
            local_vertices = np.array(local_points, dtype=np.float32)

            # 获取变换矩阵
            try:
                from isaacsim.core.utils.transformations import get_relative_transform
                transform_matrix = get_relative_transform(
                    self.state.target_collision_prim.GetPrim(),
                    self.state.coord_prim
                )
            except ImportError:
                # 如果导入失败，使用备用方法
                carb.log_warn("⚠️  无法导入transformations模块，使用单位矩阵")
                transform_matrix = np.eye(4)

            # 应用变换
            # 添加齐次坐标
            homogeneous_vertices = np.hstack([
                local_vertices,
                np.ones((local_vertices.shape[0], 1))
            ])

            # 应用变换矩阵
            transformed_vertices = (transform_matrix @ homogeneous_vertices.T).T

            # 返回前三列（去掉齐次坐标）
            return transformed_vertices[:, :3]

        except Exception as e:
            carb.log_error(f"❌ 备用顶点获取方法失败: {e}")
            return None

    def _calculate_volume_and_buoyancy_center(self) -> Optional[Dict]:
        """
        计算物体的体积和浮心

        Returns:
            Dict: 包含总体积、水下体积、浮心坐标等信息，失败时返回None
        """
        try:
            # 验证 mesh prim 是否仍然有效
            if not self.state.target_collision_prim or not self.state.target_collision_prim.GetPrim().IsValid():
                carb.log_error("❌ target_collision_prim 无效")
                return None

            # 尝试使用 mesh_utils，如果失败则使用备用方法
            try:
                world_vertices = mesh_utils.get_mesh_vertices_relative_to(
                    self.state.target_collision_prim, self.state.coord_prim
                )
            except RuntimeError as e:
                if "Accessed schema on invalid prim" in str(e):
                    print("⚠️  mesh_utils 方法失败，使用备用方法获取顶点...")
                    world_vertices = self._get_mesh_vertices_safe()
                    if world_vertices is None:
                        return None
                else:
                    raise e

            self.physics.world_vertices_record = world_vertices

            # 获取到淹没体积和浮心
            total, below, cb = slice_convex_poly_volume(
                world_vertices,
                plane_normal=self.config.water_plane_normal,
                plane_d=self.config.water_surface_z
            )

            self.time_flag += 1
            volume_data = {
                'total_volume': total,
                'submerged_volume': below,
                'center_buoyancy': cb,
                'time_flag': self.time_flag,
                'world_vertices': world_vertices
            }

            # 调试输出
            if self.water_volume_fps:
                print("-" * 65 + "进入回调函数")
                print(f"\n--- [帧 {self.sim_frame_count}] ---")
                print(f"💧 水下体积计算结果:")
                print(f"   - 总 体 积: {volume_data['total_volume']:.6f} m³")
                print(f"   - 水下体积: {volume_data['submerged_volume']:.6f} m³")
                print(f"   - 浮心坐标: {volume_data['center_buoyancy']}")

            return volume_data

        except Exception as e:
            carb.log_error(f"❌ 体积计算失败: {e}")
            return None

    def _calculate_buoyancy_force(self, volume_data: Dict) -> Optional[Tuple[np.ndarray, np.ndarray, np.ndarray]]:
        """
        计算浮力

        Args:
            volume_data: 体积数据字典

        Returns:
            Tuple[force, torque, position]: 浮力向量、力矩、作用点，失败时返回None
        """
        if volume_data['submerged_volume'] <= self.config.VOLUME_CALCULATION_THRESHOLD:
            return None

        try:
            # 计算浮力大小：F = ρ * V * g
            F_buoyancy_magnitude = (self.physics.water_density *
                                  volume_data['submerged_volume'] *
                                  self.physics.gravity_magnitude)

            # 浮力方向（向上）
            buoyancy_direction = np.array([0.0, 0.0, 1.0])
            buoyancy_force_vector = F_buoyancy_magnitude * buoyancy_direction

            # 准备施加力的参数
            forces_buoyancy = np.array([buoyancy_force_vector], dtype=np.float32)
            torques_buoyancy = np.zeros((1, 3), dtype=np.float32)
            pos_buoyancy = np.array([volume_data['center_buoyancy']], dtype=np.float32)

            if self.config.debug_mode:
                print(f"💧 浮力计算:")
                print(f"   - 浮力向量: {buoyancy_force_vector}")
                print(f"   - 浮力大小: {F_buoyancy_magnitude:.2f} N")
                print(f"   - 作用点: {volume_data['center_buoyancy']}")

            return forces_buoyancy, torques_buoyancy, pos_buoyancy

        except Exception as e:
            carb.log_error(f"❌ 浮力计算失败: {e}")
            return None

    def _calculate_damping_forces(self, volume_data: Dict,
                                linear_velocity: np.ndarray,
                                angular_velocity: np.ndarray) -> Optional[Tuple[np.ndarray, np.ndarray]]:
        """
        计算阻尼力和阻尼力矩

        Args:
            volume_data: 体积数据字典
            linear_velocity: 线速度
            angular_velocity: 角速度

        Returns:
            Tuple[linear_damping_force, angular_damping_torque]: 阻尼力和力矩，失败时返回None
        """
        if volume_data['submerged_volume'] <= self.config.ANGULAR_VELOCITY_THRESHOLD:
            return None

        try:
            # 计算淹没比例
            total_volume = volume_data['total_volume']
            submerged_ratio = min(volume_data['submerged_volume'] / total_volume, 1.0)

            # 获取速度的大小
            velocity_magnitude = np.linalg.norm(linear_velocity)
            angular_velocity_magnitude = np.linalg.norm(angular_velocity)

            # 🌊 选择阻尼系统计算阻尼力
            if self.damping.use_hydrodynamic_damping and self.damping.hydrodynamic_damping_system:
                # 🌊 使用水动力学阻尼系统
                try:
                    F_drag_linear, T_drag_angular = self.damping.hydrodynamic_damping_system.get_damping_forces(
                        linear_velocity=linear_velocity[0],  # 取第一个环境的速度
                        angular_velocity=angular_velocity[0]  # 取第一个环境的角速度
                    )

                    # 应用淹没比例
                    F_drag_linear = F_drag_linear * submerged_ratio
                    T_drag_angular = T_drag_angular * submerged_ratio

                    # 调试信息
                    if self.config.debug_mode:
                        try:
                            damping_result = self.damping.hydrodynamic_damping_system.calculate_hydrodynamic_damping(
                                linear_velocity[0], angular_velocity[0]
                            )
                            debug_info = damping_result['debug_info']

                            print(f"🌊 水动力学阻尼信息:")
                            print(f"  线速度: {debug_info['linear_speed']:.3f} m/s")
                            print(f"  平滑线速度: {debug_info['smooth_linear_speed']:.3f} m/s")
                            print(f"  角速度: {debug_info['angular_speed']:.3f} rad/s")
                            print(f"  速度区间: {debug_info['velocity_zone']}")
                            print(f"  雷诺数: {debug_info['reynolds_number']:.0f}")
                        except Exception as debug_e:
                            print(f"⚠️  水动力学调试信息获取失败: {debug_e}")

                except Exception as e:
                    print(f"❌ 水动力学阻尼计算失败: {e}")
                    print("🔧 回退到传统阻尼计算...")
                    self.damping.use_hydrodynamic_damping = False
                    return self._calculate_traditional_damping(
                        linear_velocity, angular_velocity, velocity_magnitude,
                        angular_velocity_magnitude, submerged_ratio
                    )
            else:
                # 🔧 传统固定阻尼计算（备用）
                return self._calculate_traditional_damping(
                    linear_velocity, angular_velocity, velocity_magnitude,
                    angular_velocity_magnitude, submerged_ratio
                )

            return F_drag_linear, T_drag_angular

        except Exception as e:
            carb.log_error(f"❌ 阻尼力计算失败: {e}")
            return None

    def _calculate_traditional_damping(self, linear_velocity: np.ndarray, angular_velocity: np.ndarray,
                                     velocity_magnitude: float, angular_velocity_magnitude: float,
                                     submerged_ratio: float) -> Tuple[np.ndarray, np.ndarray]:
        """
        传统阻尼计算方法（备用）

        Returns:
            Tuple[linear_damping_force, angular_damping_torque]: 阻尼力和力矩
        """
        # 线性阻尼计算
        if velocity_magnitude > self.config.VELOCITY_THRESHOLD:
            # 线性阻尼分量
            linear_drag_component = self.damping.damping_config['linear_drag_coefficient'] * linear_velocity

            # 二次阻尼分量 (与速度平方成正比，方向与速度相反)
            velocity_direction = linear_velocity / velocity_magnitude
            quadratic_drag_component = (self.damping.damping_config['quadratic_drag_coefficient'] *
                                      velocity_magnitude * velocity_magnitude *
                                      velocity_direction)

            # 总阻尼力 = -(线性阻尼 + 二次阻尼) * 淹没比例
            F_drag_linear = -(linear_drag_component + quadratic_drag_component) * submerged_ratio
        else:
            F_drag_linear = np.zeros_like(linear_velocity)

        # 角阻尼力矩计算
        if angular_velocity_magnitude > self.config.ANGULAR_VELOCITY_THRESHOLD:
            # 线性角阻尼分量
            linear_angular_drag = self.damping.damping_config['angular_drag_coefficient'] * angular_velocity

            # 二次角阻尼分量
            angular_velocity_direction = angular_velocity / angular_velocity_magnitude
            quadratic_angular_drag = (self.damping.damping_config['angular_quadratic_drag_coefficient'] *
                                    angular_velocity_magnitude * angular_velocity_magnitude *
                                    angular_velocity_direction)

            # 总角阻尼力矩 = -(线性角阻尼 + 二次角阻尼) * 淹没比例
            T_drag_angular = -(linear_angular_drag + quadratic_angular_drag) * submerged_ratio

            # 额外的稳定化阻尼（针对倾转和俯仰）
            stability_factor = np.array([3.0, 3.0, 1.0])  # X,Y轴强额外阻尼，Z轴正常
            T_drag_angular = T_drag_angular * stability_factor
        else:
            T_drag_angular = np.zeros_like(angular_velocity)

        return F_drag_linear, T_drag_angular

    def _apply_forces_to_robot(self, buoyancy_data: Optional[Tuple],
                              damping_data: Optional[Tuple],
                              robot_pose: Tuple) -> None:
        """
        将计算出的浮力和阻尼力施加到机器人上

        Args:
            buoyancy_data: 浮力数据 (force, torque, position)
            damping_data: 阻尼力数据 (linear_force, angular_torque)
            robot_pose: 机器人位姿 (position, orientation)
        """
        try:
            # 施加浮力
            if buoyancy_data is not None:
                forces_buoyancy, torques_buoyancy, pos_buoyancy = buoyancy_data
                self.state.rigid_link.apply_forces_and_torques_at_pos(
                    forces=forces_buoyancy,
                    torques=torques_buoyancy,
                    positions=pos_buoyancy,
                    is_global=True
                )

            # 施加阻尼力
            if damping_data is not None:
                F_drag_linear, T_drag_angular = damping_data
                self.state.rigid_link.apply_forces_and_torques_at_pos(
                    forces=F_drag_linear,
                    torques=T_drag_angular,
                    positions=robot_pose[0],
                    is_global=True
                )

                if self.config.debug_mode:
                    print(f"🔧 阻尼力施加:")
                    print(f"   - 线性阻尼力: {F_drag_linear}")
                    print(f"   - 角阻尼力矩: {T_drag_angular}")

        except Exception as e:
            carb.log_error(f"❌ 力施加失败: {e}")

    def _update_simulation_data(self, volume_data: Dict,
                               linear_velocity: np.ndarray,
                               robot_pose: Tuple) -> None:
        """
        更新仿真数据记录

        Args:
            volume_data: 体积数据
            linear_velocity: 线速度
            robot_pose: 机器人位姿
        """
        try:
            # 更新物理数据
            self.physics.last_volume_data = volume_data
            self.physics.buoyancy_force = (volume_data['submerged_volume'] *
                                         self.physics.water_density *
                                         self.physics.gravity_magnitude)

            # 记录数据日志
            self.data_log["sim_frame"].append(self.sim_frame_count)
            self.data_log["com_position"].append(robot_pose[0])
            self.data_log['linear_velocity'].append(linear_velocity)
            self.data_log['quaternion_orientation'].append(robot_pose[1])

            # 更新机器人状态
            self.base_link_pose = {
                'position': self.state.iris_robot.get_world_poses(),
                'time_flag': self.time_flag
            }

            if self.config.debug_mode:
                print(f"📊 仿真数据更新:")
                print(f"   - 重心: {robot_pose[0]}")
                print(f"   - 四元数: {robot_pose[1]}")
                print(f"   - 浮力: {self.physics.buoyancy_force:.2f} N")

        except Exception as e:
            carb.log_error(f"❌ 数据更新失败: {e}")

    def _volume_calculation_callback(self, step_size: float):
        """
        物理回调函数 - 重构版本

        主要职责：
        1. 协调各个计算模块
        2. 处理异常情况
        3. 控制计算流程

        Args:
            step_size: 物理步长（秒）
        """
        self.sim_frame_count += 1

        # 🔍 前置检查
        if not self._validate_simulation_state():
            return

        try:
            # 🧮 1. 计算体积和浮心
            volume_data = self._calculate_volume_and_buoyancy_center()
            if volume_data is None:
                self.physics.last_volume_data = None
                return

            # 🚀 2. 获取机器人运动状态
            linear_velocity = self.state.rigid_link.get_linear_velocities()
            angular_velocity = self.state.rigid_link.get_angular_velocities()
            robot_pose = self.state.rigid_link.get_world_poses()

            # 💧 3. 计算浮力
            buoyancy_data = self._calculate_buoyancy_force(volume_data)

            # 🌊 4. 计算阻尼力
            damping_data = self._calculate_damping_forces(volume_data, linear_velocity, angular_velocity)

            # ⚡ 5. 施加力到机器人
            self._apply_forces_to_robot(buoyancy_data, damping_data, robot_pose)

            # 📊 6. 更新仿真数据
            self._update_simulation_data(volume_data, linear_velocity, robot_pose)

        except Exception as e:
            carb.log_error(f"❌ 体积计算回调失败: {e}")
            self.physics.last_volume_data = None

    def _validate_simulation_state(self) -> bool:
        """
        验证仿真状态是否有效

        Returns:
            bool: 状态是否有效
        """
        if not self.state.target_collision_prim:
            self.physics.last_volume_data = None
            return False

        if not self.state.coord_prim or not self.state.coord_prim.IsValid():
            carb.log_error("❌ coord_prim 无效")
            self.physics.last_volume_data = None
            return False

        return True

    def step(self):
        """执行单步仿真逻辑"""
        # 步进物理仿真和渲染
        self.state.world.step(render=True)


def main():
    """主函数 - 统一参数配置和仿真流程控制"""

    # ========================================
    # 🎛️ 统一参数配置区域
    # ========================================
    
    # 📁 文件路径配置
    ENV_USD_PATH = "/home/<USER>/Learn_standalone_isaac/usd_assets/ground_water.usd"
    ROBOT_USD_PATH = "/home/<USER>/Learn_standalone_isaac/usd_assets/iris_fov.usd"
    
    # 🌊 水环境参数
    WATER_SURFACE_Z = 0.0  # 水面高度为 Z=0.5 米

    # ========================================
    # 🚀 仿真初始化和运行
    # ========================================

    sim = IrisBuoyancySim()

    # 应用配置
    config_to_apply = {
        'water_surface_z': WATER_SURFACE_Z
    }
    sim.apply_config(config_to_apply)

    # 设置场景
    if not sim.setup_scene(environment_usd_path=ENV_USD_PATH, robot_usd_path=ROBOT_USD_PATH):
        carb.log_error("❌ 场景设置失败，程序退出。")
        simulation_app.close()
        return

    print("\n🎉 仿真设置完成，开始实时计算循环...")

    try:
        while simulation_app.is_running():
            sim.step()
    except KeyboardInterrupt:
        print("\n用户手动中断仿真。")
    except Exception as e:
        carb.log_error(f"❌ 仿真循环中出现严重错误: {e}")
    finally:
        print("关闭仿真程序。")
        print(f"💾 正在保存仿真数据到 '{sim.config.record_data_filename}'...")
        np.savez(sim.config.record_data_filename, **sim.data_log)
        print("✅ 数据保存完成。")
        simulation_app.close()

def test_refactored_methods():
    """测试重构后的方法是否正常工作"""
    print("🧪 测试重构后的方法...")

    # 创建仿真实例
    sim = IrisBuoyancySim()

    # 测试配置应用
    test_config = {'water_surface_z': 0.5, 'debug_mode': False}
    sim.apply_config(test_config)

    print("✅ 配置应用测试通过")
    print("✅ 数据结构重构测试通过")
    print("✅ 方法拆分测试通过")

    return True

if __name__ == "__main__":
    # 可以选择运行测试或主程序
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        test_refactored_methods()
    else:
        main()
