"""
水动力学阻尼系统 - 真实的水中运动特性
Hydrodynamic Damping System - Realistic underwater motion characteristics

基于真实的流体力学和水动力学原理设计
"""

import numpy as np
from typing import Dict, Tuple, Optional
import math

class HydrodynamicDampingSystem:
    def __init__(self, mass: float, inertia: list, characteristic_length: float):
        """
        初始化水动力学阻尼系统
        
        Args:
            mass: 物体质量 (kg)
            inertia: 对角惯性矩 [Ixx, Iyy, Izz] (kg·m²)
            characteristic_length: 特征长度 (m)
        """
        self.mass = mass
        self.inertia = np.array(inertia)
        self.char_length = characteristic_length
        
        # 🌊 水动力学参数
        self.fluid_density = 1000.0  # 水密度 kg/m³
        self.kinematic_viscosity = 1e-6  # 水的运动粘度 m²/s
        
        # 📊 基于真实水动力学的阻尼系数
        self.drag_coefficients = self._calculate_realistic_drag_coefficients()
        
        # 🎯 速度分层参数（优化低速稳定性）
        self.velocity_zones = {
            'very_low': {'threshold': 0.05, 'damping_factor': 0.4},    # 🔧 增加低速阻尼，减少震荡
            'low': {'threshold': 0.2, 'damping_factor': 0.5},          # 🔧 适中阻尼，保持稳定
            'medium': {'threshold': 0.8, 'damping_factor': 0.7},       # 🔧 稍微增加
            'high': {'threshold': 2.0, 'damping_factor': 1.0},         # 标准阻尼
            'very_high': {'threshold': float('inf'), 'damping_factor': 1.3}  # 🔧 稍微降低高速阻尼
        }
        
        # 🔄 平滑过渡参数
        self.smoothing_config = {
            'velocity_smoothing': 0.15,      # 速度平滑因子（更敏感）
            'damping_smoothing': 0.08,       # 阻尼平滑因子（更平滑）
            'transition_width': 0.1,         # 过渡区间宽度
            'angular_responsiveness': 2.0,   # 角度响应倍数
        }
        
        # 📈 历史记录
        self.velocity_history = []
        self.angular_velocity_history = []
        self.damping_factor_history = []
        self.angular_damping_factor_history = []
        self.damping_force_history = []      # 🎯 新增：阻尼力历史
        self.angular_torque_history = []     # 🎯 新增：角阻尼力矩历史
        self.history_length = 15
        
        # 🔧 添加调试开关
        self._debug_drag_calculation = True  # 可以通过参数控制

        print(f"🌊 水动力学阻尼系统初始化完成 (优化版本)")
        print(f"  基础线性阻尼: {self.drag_coefficients['linear']:.2f} N·s/m")
        print(f"  基础二次阻尼: {self.drag_coefficients['quadratic']:.2f} N·s²/m²")
        print(f"  基础角阻尼: {self.drag_coefficients['angular_linear']:.2f} N·m·s/rad")
        print(f"  基础角二次阻尼: {self.drag_coefficients['angular_quadratic']:.2f} N·m·s²/rad²")

        # 🔍 验证量纲正确性
        self._validate_drag_coefficients()
    
    def _calculate_realistic_drag_coefficients(self) -> Dict[str, float]:
        """基于真实水动力学计算阻尼系数 - 优化版本"""

        # 估算物体的特征面积和体积
        frontal_area = self.char_length ** 2  # 迎风面积 [m²]
        volume = self.mass / 800.0  # 假设物体密度约800 kg/m³ [m³]

        # 🌊 线性阻尼（粘性阻力）- 优化的多方法计算
        linear_drag = self._calculate_optimized_linear_damping(frontal_area, volume)
        
        # 🌊 二次阻尼（惯性阻力）- 基于阻力公式 F = 0.5 * ρ * Cd * A * v²
        # 这是高速时的主要阻力
        drag_coefficient_cd = 0.8  # 典型的钝体阻力系数
        quadratic_drag = 0.5 * self.fluid_density * drag_coefficient_cd * frontal_area
        
        # 🔄 角阻尼系数 - 修复量纲问题
        # 角线性阻尼（低角速度）：[N·s/m] × [m²] = [N·m·s/rad]
        angular_linear = linear_drag * (self.char_length ** 2) / 6.0

        # 🔧 角二次阻尼（高角速度）：[N·s²/m²] × [m³] = [N·m·s²/rad²]
        # 修复：确保正确的量纲转换
        angular_quadratic = quadratic_drag * (self.char_length ** 3) / (8.0 * self.char_length)
        # 简化：angular_quadratic = quadratic_drag * (self.char_length ** 2) / 8.0
        
        return {
            'linear': linear_drag,
            'quadratic': quadratic_drag,
            'angular_linear': angular_linear,
            'angular_quadratic': angular_quadratic
        }

    def _calculate_optimized_linear_damping(self, frontal_area: float, volume: float) -> float:
        """
        优化的线性阻尼计算 - 多方法综合

        Args:
            frontal_area: 迎风面积 [m²]
            volume: 物体体积 [m³]

        Returns:
            linear_drag: 线性阻尼系数 [N·s/m]
        """

        # 方法1：修正的斯托克斯阻尼（适用于低雷诺数）
        dynamic_viscosity = self.kinematic_viscosity * self.fluid_density  # [Pa·s]
        equivalent_radius = (3 * volume / (4 * math.pi)) ** (1/3)  # 等效球体半径 [m]
        stokes_drag = 6 * math.pi * dynamic_viscosity * equivalent_radius  # [N·s/m]

        # 方法2：基于特征长度的经验公式（适用于复杂几何体）
        shape_factor = 1.5  # 复杂几何体的形状因子（相对于球体）
        empirical_drag = shape_factor * stokes_drag

        # 方法3：基于表面积的粘性阻尼
        surface_area = 6 * frontal_area  # 估算总表面积（假设长宽高比例）
        surface_drag = dynamic_viscosity * surface_area / self.char_length  # [N·s/m]

        # 方法4：基于质量的最小阻尼（确保稳定性）
        min_damping_coefficient = 8.0  # [N·s/m] - 经验值，确保低速稳定性
        mass_based_drag = min_damping_coefficient * (self.mass / 1.0)  # 质量归一化

        # 🎯 综合选择：取较大值确保稳定性
        candidates = [stokes_drag, empirical_drag, surface_drag, mass_based_drag]
        linear_drag = max(candidates)

        # 📊 调试信息
        if hasattr(self, '_debug_drag_calculation'):
            print(f"🔧 线性阻尼计算详情:")
            print(f"  斯托克斯阻尼: {stokes_drag:.2f} N·s/m")
            print(f"  经验修正阻尼: {empirical_drag:.2f} N·s/m")
            print(f"  表面积阻尼: {surface_drag:.2f} N·s/m")
            print(f"  质量基础阻尼: {mass_based_drag:.2f} N·s/m")
            print(f"  最终选择: {linear_drag:.2f} N·s/m")

        return linear_drag

    def _validate_drag_coefficients(self) -> None:
        """验证阻尼系数的量纲和数值合理性"""
        coeffs = self.drag_coefficients

        # 🔍 量纲验证
        print(f"🔍 阻尼系数验证:")

        # 线性阻尼：应该是 [N·s/m] = [kg/s]
        if coeffs['linear'] <= 0:
            print(f"  ⚠️  线性阻尼系数异常: {coeffs['linear']}")
        else:
            print(f"  ✅ 线性阻尼: {coeffs['linear']:.2f} N·s/m")

        # 二次阻尼：应该是 [N·s²/m²] = [kg/m]
        if coeffs['quadratic'] <= 0:
            print(f"  ⚠️  二次阻尼系数异常: {coeffs['quadratic']}")
        else:
            print(f"  ✅ 二次阻尼: {coeffs['quadratic']:.2f} N·s²/m²")

        # 角线性阻尼：应该是 [N·m·s/rad] = [kg·m²/s]
        if coeffs['angular_linear'] <= 0:
            print(f"  ⚠️  角线性阻尼系数异常: {coeffs['angular_linear']}")
        else:
            print(f"  ✅ 角线性阻尼: {coeffs['angular_linear']:.2f} N·m·s/rad")

        # 角二次阻尼：应该是 [N·m·s²/rad²] = [kg·m²/s²]
        if coeffs['angular_quadratic'] <= 0:
            print(f"  ⚠️  角二次阻尼系数异常: {coeffs['angular_quadratic']}")
        else:
            print(f"  ✅ 角二次阻尼: {coeffs['angular_quadratic']:.2f} N·m·s²/rad²")

        # 🎯 合理性检查
        mass_ratio = coeffs['linear'] / self.mass if self.mass > 0 else 0
        if mass_ratio < 0.1 or mass_ratio > 100:
            print(f"  ⚠️  线性阻尼/质量比异常: {mass_ratio:.2f}")
        else:
            print(f"  ✅ 阻尼/质量比合理: {mass_ratio:.2f}")

    def _get_velocity_zone_factor(self, velocity_magnitude: float) -> float:
        """
        根据速度大小获取阻尼因子（带平滑过渡）
        使用分段线性插值实现平滑衔接
        """
        # 🎯 定义速度-阻尼因子的控制点
        velocity_points = [0.0, 0.05, 0.2, 0.8, 2.0, 5.0]
        damping_points = [0.1, 0.1, 0.3, 0.6, 1.0, 1.5]

        # 边界处理
        if velocity_magnitude <= velocity_points[0]:
            return damping_points[0]
        if velocity_magnitude >= velocity_points[-1]:
            return damping_points[-1]

        # 🔄 分段线性插值
        for i in range(len(velocity_points) - 1):
            v1, v2 = velocity_points[i], velocity_points[i + 1]
            d1, d2 = damping_points[i], damping_points[i + 1]

            if v1 <= velocity_magnitude <= v2:
                # 线性插值公式
                ratio = (velocity_magnitude - v1) / (v2 - v1)
                return d1 + ratio * (d2 - d1)

        return damping_points[-1]

    def _get_smooth_velocity_zone_factor(self, velocity_magnitude: float) -> float:
        """
        使用S型曲线（Sigmoid）实现更自然的速度区间过渡
        这种方法比线性插值更平滑，避免了导数不连续
        """
        # 🌊 S型过渡函数参数
        transitions = [
            {'center': 0.125, 'width': 0.075, 'from': 0.1, 'to': 0.3},   # very_low → low
            {'center': 0.5, 'width': 0.3, 'from': 0.3, 'to': 0.6},       # low → medium
            {'center': 1.4, 'width': 0.6, 'from': 0.6, 'to': 1.0},       # medium → high
            {'center': 3.5, 'width': 1.5, 'from': 1.0, 'to': 1.5},       # high → very_high
        ]

        # 基础值（最低速度区间）
        damping_factor = 0.1

        # 🔄 逐个应用S型过渡
        for transition in transitions:
            center = transition['center']
            width = transition['width']
            from_val = transition['from']
            to_val = transition['to']

            # S型函数：f(x) = 1 / (1 + exp(-k*(x-center)))
            # k = 4/width 控制过渡的陡峭程度
            k = 4.0 / width
            sigmoid = 1.0 / (1.0 + math.exp(-k * (velocity_magnitude - center)))

            # 如果当前速度在这个过渡区间内，应用过渡
            if from_val <= damping_factor <= to_val or velocity_magnitude >= center - width:
                damping_factor = from_val + sigmoid * (to_val - from_val)

        return damping_factor

    def _get_ultra_smooth_velocity_zone_factor(self, velocity_magnitude: float) -> float:
        """
        使用三次Hermite插值实现超平滑的速度区间过渡
        确保函数值和一阶导数都连续
        """
        # 🎯 定义控制点和导数（进一步优化低速稳定性）
        control_points = [
            {'v': 0.0, 'd': 0.6, 'slope': 0.0},      # 🔧 起始点提高到0.6，强化极低速稳定性
            {'v': 0.03, 'd': 0.6, 'slope': 0.0},     # 🔧 极低速平台，确保稳定
            {'v': 0.05, 'd': 0.6, 'slope': 0.5},     # 🔧 very_low结束，开始增长
            {'v': 0.125, 'd': 0.65, 'slope': 0.8},   # 🔧 过渡中点，缓慢增长
            {'v': 0.2, 'd': 0.7, 'slope': 0.6},      # 🔧 low结束，稳定阻尼
            {'v': 0.5, 'd': 0.8, 'slope': 0.4},      # 🔧 中间点，适中阻尼
            {'v': 0.8, 'd': 0.9, 'slope': 0.3},      # 🔧 medium结束，稳定过渡
            {'v': 1.4, 'd': 1.0, 'slope': 0.2},      # 🔧 过渡点，缓慢增长
            {'v': 2.0, 'd': 1.1, 'slope': 0.1},      # 🔧 high结束，平缓
            {'v': 4.0, 'd': 1.2, 'slope': 0.05},     # 🔧 高速点，适中阻尼
            {'v': 6.0, 'd': 1.25, 'slope': 0.0},     # 🔧 最终点，适中饱和
        ]

        # 边界处理
        if velocity_magnitude <= control_points[0]['v']:
            return control_points[0]['d']
        if velocity_magnitude >= control_points[-1]['v']:
            return control_points[-1]['d']

        # 🔄 找到插值区间
        for i in range(len(control_points) - 1):
            p1, p2 = control_points[i], control_points[i + 1]

            if p1['v'] <= velocity_magnitude <= p2['v']:
                # 三次Hermite插值
                x1, y1, m1 = p1['v'], p1['d'], p1['slope']
                x2, y2, m2 = p2['v'], p2['d'], p2['slope']

                # 🔧 归一化参数 t ∈ [0,1] - 添加除零保护
                if abs(x2 - x1) < 1e-10:
                    return y1  # 避免除零错误
                t = (velocity_magnitude - x1) / (x2 - x1)
                t = max(0.0, min(1.0, t))  # 确保 t 在 [0,1] 范围内

                # Hermite基函数
                h00 = 2*t**3 - 3*t**2 + 1      # (1,0,0,0)
                h10 = t**3 - 2*t**2 + t        # (0,1,0,0)
                h01 = -2*t**3 + 3*t**2         # (0,0,1,0)
                h11 = t**3 - t**2              # (0,0,0,1)

                # 插值结果
                result = (h00 * y1 +
                         h10 * (x2 - x1) * m1 +
                         h01 * y2 +
                         h11 * (x2 - x1) * m2)

                return max(0.05, result)  # 确保最小阻尼

        return control_points[-1]['d']

    def _smooth_transition(self, current_value: float, target_value: float,
                          smoothing_factor: float) -> float:
        """平滑过渡函数"""
        return smoothing_factor * target_value + (1 - smoothing_factor) * current_value
    
    def _calculate_smooth_damping_factor(self, velocity_magnitude: float, 
                                       is_angular: bool = False) -> float:
        """计算平滑的阻尼因子"""
        
        # 🎯 获取超平滑的阻尼因子（使用三次Hermite插值）
        target_factor = self._get_ultra_smooth_velocity_zone_factor(velocity_magnitude)
        
        # 获取历史记录
        if is_angular:
            history = self.angular_damping_factor_history
        else:
            history = self.damping_factor_history
        
        if not history:
            return target_factor
        
        # 平滑过渡
        last_factor = history[-1]
        smoothing = self.smoothing_config['damping_smoothing']
        
        # 🎯 速度变化越大，过渡越快
        if len(history) >= 2:
            velocity_change_rate = abs(target_factor - last_factor)
            adaptive_smoothing = max(smoothing, min(0.3, smoothing + velocity_change_rate * 0.5))
        else:
            adaptive_smoothing = smoothing
        
        smooth_factor = self._smooth_transition(last_factor, target_factor, adaptive_smoothing)
        
        return smooth_factor
    
    def _calculate_reynolds_based_damping(self, velocity_magnitude: float) -> Tuple[float, float]:
        """基于雷诺数的阻尼计算"""
        
        if velocity_magnitude < 1e-6:
            return 0.0, 0.0
        
        # 计算雷诺数
        reynolds_number = velocity_magnitude * self.char_length / self.kinematic_viscosity
        
        # 🌊 基于雷诺数的阻力系数调整
        if reynolds_number < 1:  # 斯托克斯流区域
            cd_factor = 1.0
            linear_dominance = 0.9  # 线性阻力占主导
        elif reynolds_number < 1000:  # 过渡区域
            cd_factor = 1.0 + 0.1 * math.log10(reynolds_number)
            linear_dominance = 0.7 - 0.0005 * reynolds_number
        else:  # 惯性区域
            cd_factor = 1.2
            linear_dominance = max(0.1, 0.7 - 0.0005 * reynolds_number)
        
        linear_dominance = max(0.1, min(0.9, linear_dominance))
        quadratic_dominance = 1.0 - linear_dominance
        
        return linear_dominance, quadratic_dominance * cd_factor
    
    def calculate_hydrodynamic_damping(self, linear_velocity: np.ndarray, 
                                     angular_velocity: np.ndarray) -> Dict[str, np.ndarray]:
        """
        计算水动力学阻尼
        
        Args:
            linear_velocity: 线速度 [vx, vy, vz] (m/s)
            angular_velocity: 角速度 [wx, wy, wz] (rad/s)
            
        Returns:
            Dict包含阻尼系数和调试信息
        """
        
        # 平滑速度信号
        if self.velocity_history:
            smooth_linear_vel = self._smooth_transition(
                self.velocity_history[-1], linear_velocity, 
                self.smoothing_config['velocity_smoothing']
            )
        else:
            smooth_linear_vel = linear_velocity.copy()
            
        if self.angular_velocity_history:
            smooth_angular_vel = self._smooth_transition(
                self.angular_velocity_history[-1], angular_velocity,
                self.smoothing_config['velocity_smoothing']
            )
        else:
            smooth_angular_vel = angular_velocity.copy()
        
        # 更新历史记录
        self.velocity_history.append(smooth_linear_vel.copy())
        self.angular_velocity_history.append(smooth_angular_vel.copy())
        
        if len(self.velocity_history) > self.history_length:
            self.velocity_history.pop(0)
        if len(self.angular_velocity_history) > self.history_length:
            self.angular_velocity_history.pop(0)
        
        # 计算速度大小
        linear_speed = np.linalg.norm(smooth_linear_vel)
        angular_speed = np.linalg.norm(smooth_angular_vel)
        
        # 🌊 基于雷诺数的阻尼计算
        linear_dominance, quadratic_factor = self._calculate_reynolds_based_damping(linear_speed)
        
        # 计算平滑的阻尼因子
        linear_damping_factor = self._calculate_smooth_damping_factor(linear_speed, False)
        angular_damping_factor = self._calculate_smooth_damping_factor(
            angular_speed, True
        ) * self.smoothing_config['angular_responsiveness']
        
        # 更新阻尼因子历史
        self.damping_factor_history.append(linear_damping_factor)
        self.angular_damping_factor_history.append(angular_damping_factor)
        
        if len(self.damping_factor_history) > self.history_length:
            self.damping_factor_history.pop(0)
        if len(self.angular_damping_factor_history) > self.history_length:
            self.angular_damping_factor_history.pop(0)
        
        # 🎯 计算最终阻尼系数
        # 线性阻尼（考虑雷诺数效应）
        effective_linear_damping = (
            self.drag_coefficients['linear'] * linear_dominance * linear_damping_factor
        )
        
        # 二次阻尼（考虑雷诺数效应）
        effective_quadratic_damping = (
            self.drag_coefficients['quadratic'] * quadratic_factor * linear_damping_factor
        )
        
        # 角阻尼
        effective_angular_linear = (
            self.drag_coefficients['angular_linear'] * angular_damping_factor
        )
        effective_angular_quadratic = (
            self.drag_coefficients['angular_quadratic'] * angular_damping_factor
        )
        
        # 轴向差异化（Z轴阻尼稍小，允许更多垂直运动）
        linear_damping = np.array([
            effective_linear_damping,
            effective_linear_damping, 
            effective_linear_damping * 0.8  # Z轴阻尼更小
        ])
        
        quadratic_damping = np.array([
            effective_quadratic_damping,
            effective_quadratic_damping,
            effective_quadratic_damping * 0.8
        ])
        
        angular_damping = np.array([
            effective_angular_linear * 0.8,   # 俯仰：适中阻尼
            effective_angular_linear * 0.8,   # 横滚：适中阻尼
            effective_angular_linear * 0.6    # 偏航：更小阻尼
        ])
        
        angular_quadratic_damping = np.array([
            effective_angular_quadratic * 0.8,
            effective_angular_quadratic * 0.8,
            effective_angular_quadratic * 0.6
        ])
        
        # 调试信息
        debug_info = {
            'linear_speed': linear_speed,
            'angular_speed': angular_speed,
            'smooth_linear_speed': np.linalg.norm(smooth_linear_vel),
            'smooth_angular_speed': np.linalg.norm(smooth_angular_vel),
            'linear_damping_factor': linear_damping_factor,
            'angular_damping_factor': angular_damping_factor,
            'reynolds_number': linear_speed * self.char_length / self.kinematic_viscosity,
            'linear_dominance': linear_dominance,
            'quadratic_factor': quadratic_factor,
            'velocity_zone': self._get_velocity_zone_name(linear_speed)
        }
        
        return {
            'linear_damping': linear_damping,
            'quadratic_damping': quadratic_damping,
            'angular_damping': angular_damping,
            'angular_quadratic_damping': angular_quadratic_damping,
            'debug_info': debug_info
        }
    
    def _get_velocity_zone_name(self, velocity_magnitude: float) -> str:
        """获取当前速度区间名称"""
        for zone_name, zone_config in self.velocity_zones.items():
            if velocity_magnitude <= zone_config['threshold']:
                return zone_name
        return 'very_high'
    
    def get_damping_forces(self, linear_velocity: np.ndarray,
                          angular_velocity: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        计算水动力学阻尼力和力矩（带力平滑）

        Returns:
            Tuple[linear_damping_force, angular_damping_torque]
        """
        damping_result = self.calculate_hydrodynamic_damping(linear_velocity, angular_velocity)

        # 🎯 计算原始阻尼力（线性 + 二次项）- 优化版本
        linear_speed = np.linalg.norm(linear_velocity)
        if linear_speed > 1e-5:  # 提高阈值，增强数值稳定性
            velocity_direction = linear_velocity / linear_speed

            # 线性阻尼分量：F = -C_linear * v
            linear_component = -damping_result['linear_damping'] * linear_velocity

            # 🔧 二次阻尼分量：F = -C_quadratic * |v|² * v̂ (确保量纲正确)
            quadratic_component = (-damping_result['quadratic_damping'] *
                                 linear_speed**2 * velocity_direction)

            raw_linear_force = linear_component + quadratic_component
        else:
            raw_linear_force = np.zeros_like(linear_velocity)

        # 🎯 计算原始角阻尼力矩（线性 + 二次项）- 修复版本
        angular_speed = np.linalg.norm(angular_velocity)
        if angular_speed > 1e-5:  # 提高阈值，增强数值稳定性
            angular_direction = angular_velocity / angular_speed

            # 角线性阻尼分量：T = -C_linear * ω
            angular_linear_component = -damping_result['angular_damping'] * angular_velocity

            # 🔧 角二次阻尼分量：T = -C_quadratic * |ω|² * ω̂ (修复关键错误)
            angular_quadratic_component = (-damping_result['angular_quadratic_damping'] *
                                         angular_speed**2 * angular_direction)

            raw_angular_torque = angular_linear_component + angular_quadratic_component
        else:
            raw_angular_torque = np.zeros_like(angular_velocity)

        # 🌊 应用力平滑（关键改进）
        smooth_linear_force = self._apply_force_smoothing(raw_linear_force, True)
        smooth_angular_torque = self._apply_force_smoothing(raw_angular_torque, False)

        return smooth_linear_force, smooth_angular_torque

    def _apply_force_smoothing(self, raw_force: np.ndarray, is_linear: bool) -> np.ndarray:
        """
        对阻尼力进行平滑处理，避免突变

        Args:
            raw_force: 原始计算的阻尼力
            is_linear: True为线性力，False为角力矩
        """
        # 选择对应的历史记录
        if is_linear:
            history = self.damping_force_history
        else:
            history = self.angular_torque_history

        # 如果没有历史记录，直接返回
        if not history:
            # 添加到历史记录
            history.append(raw_force.copy())
            if len(history) > self.history_length:
                history.pop(0)
            return raw_force

        # 🎯 智能平滑参数
        last_force = history[-1]
        force_change = np.linalg.norm(raw_force - last_force)
        force_magnitude = np.linalg.norm(raw_force)

        # 🔧 重新设计平滑策略：低速时减少平滑，保持阻尼强度
        if force_magnitude < 1.0:  # 极小力：轻微平滑，保持阻尼效果
            smoothing_factor = 0.9  # 🔧 提高到0.9，减少平滑
        elif force_magnitude < 5.0:  # 小到中等力：适中平滑
            smoothing_factor = 0.7  # 🔧 提高到0.7
        elif force_change / max(force_magnitude, 1.0) > 0.5:  # 大变化：强平滑
            smoothing_factor = 0.3  # 🔧 适中平滑
        else:  # 正常情况：轻微平滑
            smoothing_factor = 0.6  # 🔧 提高到0.6

        # 指数移动平均平滑
        smooth_force = smoothing_factor * raw_force + (1 - smoothing_factor) * last_force

        # 更新历史记录
        history.append(smooth_force.copy())
        if len(history) > self.history_length:
            history.pop(0)

        return smooth_force
    
    def get_system_status(self) -> Dict:
        """获取系统状态"""
        status = {
            'system_type': 'hydrodynamic_damping',
            'base_linear_damping': self.drag_coefficients['linear'],
            'base_quadratic_damping': self.drag_coefficients['quadratic'],
            'base_angular_damping': self.drag_coefficients['angular_linear'],
            'base_angular_quadratic_damping': self.drag_coefficients['angular_quadratic'],
            'velocity_zones': self.velocity_zones,
            'smoothing_config': self.smoothing_config,
            'history_length': len(self.velocity_history)
        }
        
        if self.velocity_history:
            status.update({
                'current_linear_speed': np.linalg.norm(self.velocity_history[-1]),
                'current_angular_speed': np.linalg.norm(self.angular_velocity_history[-1]),
                'current_damping_factor': self.damping_factor_history[-1] if self.damping_factor_history else 1.0,
                'current_angular_damping_factor': self.angular_damping_factor_history[-1] if self.angular_damping_factor_history else 1.0
            })
        
        return status


def create_hydrodynamic_damping_system(mass: float, inertia: list, 
                                     characteristic_length: float) -> HydrodynamicDampingSystem:
    """
    创建水动力学阻尼系统
    
    Args:
        mass: 质量 (kg)
        inertia: 惯性矩 [Ixx, Iyy, Izz] (kg·m²)
        characteristic_length: 特征长度 (m)
    
    Returns:
        HydrodynamicDampingSystem 实例
    """
    return HydrodynamicDampingSystem(mass, inertia, characteristic_length)
