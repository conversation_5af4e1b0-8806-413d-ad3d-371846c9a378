#!/usr/bin/env python3
"""
调试 USD 文件结构的脚本
用于检查 prim 路径和类型
"""

# Isaac Sim标准初始化代码
from isaacsim import SimulationApp
simulation_app = SimulationApp({"headless": True})  # 无头模式，更快

import omni.usd
from pxr import UsdGeom, Usd
import isaacsim.core.utils.stage as stage_utils
from isaacsim.core.utils.stage import add_reference_to_stage

def debug_prim_structure(prim, level=0):
    """递归打印 prim 结构"""
    indent = "  " * level
    prim_type = prim.GetTypeName()
    is_mesh = prim.IsA(UsdGeom.Mesh)
    print(f"{indent}{prim.GetPath()} [{prim_type}] {'(MESH)' if is_mesh else ''}")
    
    # 如果是 Mesh，显示更多信息
    if is_mesh:
        mesh = UsdGeom.Mesh(prim)
        points_attr = mesh.GetPointsAttr()
        if points_attr:
            points = points_attr.Get()
            if points:
                print(f"{indent}  -> 顶点数量: {len(points)}")
    
    # 递归处理子级
    for child in prim.GetChildren():
        debug_prim_structure(child, level + 1)

def main():
    """主函数"""
    print("🔍 开始调试 USD 文件结构...")
    
    # 文件路径
    ENV_USD_PATH = "/home/<USER>/Learn_standalone_isaac/usd_assets/ground_water.usd"
    ROBOT_USD_PATH = "/home/<USER>/Learn_standalone_isaac/usd_assets/Cube_color.usd"
    
    try:
        # 1. 加载环境
        print(f"\n📁 加载环境文件: {ENV_USD_PATH}")
        omni.usd.get_context().open_stage(ENV_USD_PATH)
        
        # 2. 添加机器人
        robot_path = "/World/Cube_color"
        print(f"\n🤖 添加机器人到: {robot_path}")
        add_reference_to_stage(
            usd_path=ROBOT_USD_PATH, 
            prim_path=robot_path
        )
        
        # 3. 获取当前 stage
        stage = stage_utils.get_current_stage()
        
        # 4. 打印整个场景结构
        print(f"\n🌳 完整场景结构:")
        root_prim = stage.GetPseudoRoot()
        debug_prim_structure(root_prim)
        
        # 5. 检查特定路径
        target_paths = [
            "/World/Cube_color",
            "/World/Cube_color/Cube_Xform",
            "/World/Cube_color/Cube_Xform/Cube",
            "/World/world_point"
        ]
        
        print(f"\n🎯 检查特定路径:")
        for path in target_paths:
            prim = stage.GetPrimAtPath(path)
            if prim.IsValid():
                is_mesh = prim.IsA(UsdGeom.Mesh)
                print(f"✅ {path} - 类型: {prim.GetTypeName()} {'(MESH)' if is_mesh else ''}")
                
                # 如果有子级，也显示
                children = list(prim.GetChildren())
                if children:
                    print(f"   子级: {[child.GetPath() for child in children]}")
            else:
                print(f"❌ {path} - 不存在或无效")
        
        print(f"\n✅ 调试完成!")
        
    except Exception as e:
        print(f"❌ 调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        simulation_app.close()

if __name__ == "__main__":
    main()
