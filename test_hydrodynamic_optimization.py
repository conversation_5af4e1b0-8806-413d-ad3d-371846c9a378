#!/usr/bin/env python3
"""
水动力学阻尼系统优化验证测试
Test script for hydrodynamic damping system optimization
"""

import numpy as np
import matplotlib.pyplot as plt
from hydrodynamic_damping_system import create_hydrodynamic_damping_system

def test_drag_coefficient_calculation():
    """测试阻尼系数计算的正确性"""
    print("🧪 测试阻尼系数计算...")
    
    # 创建测试系统
    mass = 2.0  # kg
    inertia = [0.5, 0.5, 0.3]  # kg·m²
    char_length = 1.0  # m
    
    system = create_hydrodynamic_damping_system(mass, inertia, char_length)
    
    # 验证系数合理性
    coeffs = system.drag_coefficients
    print(f"\n📊 阻尼系数测试结果:")
    print(f"  线性阻尼: {coeffs['linear']:.4f} N·s/m")
    print(f"  二次阻尼: {coeffs['quadratic']:.4f} N·s²/m²")
    print(f"  角线性阻尼: {coeffs['angular_linear']:.4f} N·m·s/rad")
    print(f"  角二次阻尼: {coeffs['angular_quadratic']:.4f} N·m·s²/rad²")
    
    return system

def test_force_calculation_accuracy():
    """测试力计算的准确性和量纲一致性"""
    print("\n🧪 测试力计算准确性...")
    
    system = create_hydrodynamic_damping_system(2.0, [0.5, 0.5, 0.3], 1.0)
    
    # 测试不同速度下的力计算
    test_velocities = [
        np.array([0.1, 0.0, 0.0]),  # 低速
        np.array([1.0, 0.0, 0.0]),  # 中速
        np.array([5.0, 0.0, 0.0]),  # 高速
    ]
    
    test_angular_velocities = [
        np.array([0.1, 0.0, 0.0]),  # 低角速度
        np.array([1.0, 0.0, 0.0]),  # 中角速度
        np.array([5.0, 0.0, 0.0]),  # 高角速度
    ]
    
    print(f"\n📊 力计算测试结果:")
    for i, (lin_vel, ang_vel) in enumerate(zip(test_velocities, test_angular_velocities)):
        linear_force, angular_torque = system.get_damping_forces(lin_vel, ang_vel)
        
        print(f"\n  测试 {i+1}:")
        print(f"    输入线速度: {lin_vel}")
        print(f"    输入角速度: {ang_vel}")
        print(f"    输出线性力: {linear_force}")
        print(f"    输出角力矩: {angular_torque}")
        
        # 验证量纲一致性
        force_magnitude = np.linalg.norm(linear_force)
        torque_magnitude = np.linalg.norm(angular_torque)
        
        print(f"    力大小: {force_magnitude:.4f} N")
        print(f"    力矩大小: {torque_magnitude:.4f} N·m")

def test_numerical_stability():
    """测试数值稳定性"""
    print("\n🧪 测试数值稳定性...")
    
    system = create_hydrodynamic_damping_system(2.0, [0.5, 0.5, 0.3], 1.0)
    
    # 测试极小值
    tiny_velocity = np.array([1e-8, 1e-8, 1e-8])
    tiny_angular = np.array([1e-8, 1e-8, 1e-8])
    
    try:
        force, torque = system.get_damping_forces(tiny_velocity, tiny_angular)
        print(f"  ✅ 极小值测试通过")
        print(f"    极小速度力: {np.linalg.norm(force):.2e} N")
        print(f"    极小角速度力矩: {np.linalg.norm(torque):.2e} N·m")
    except Exception as e:
        print(f"  ❌ 极小值测试失败: {e}")
    
    # 测试零值
    zero_velocity = np.array([0.0, 0.0, 0.0])
    zero_angular = np.array([0.0, 0.0, 0.0])
    
    try:
        force, torque = system.get_damping_forces(zero_velocity, zero_angular)
        print(f"  ✅ 零值测试通过")
        print(f"    零速度力: {np.linalg.norm(force):.2e} N")
        print(f"    零角速度力矩: {np.linalg.norm(torque):.2e} N·m")
    except Exception as e:
        print(f"  ❌ 零值测试失败: {e}")

def test_reynolds_number_calculation():
    """测试雷诺数计算"""
    print("\n🧪 测试雷诺数计算...")
    
    system = create_hydrodynamic_damping_system(2.0, [0.5, 0.5, 0.3], 1.0)
    
    velocities = [0.01, 0.1, 1.0, 10.0]  # m/s
    
    print(f"📊 雷诺数计算结果:")
    for v in velocities:
        lin_vel = np.array([v, 0.0, 0.0])
        ang_vel = np.array([0.1, 0.0, 0.0])
        
        result = system.calculate_hydrodynamic_damping(lin_vel, ang_vel)
        re = result['debug_info']['reynolds_number']
        zone = result['debug_info']['velocity_zone']
        
        print(f"  速度 {v:5.2f} m/s -> Re = {re:8.0f}, 区间: {zone}")

def plot_damping_characteristics():
    """绘制阻尼特性曲线"""
    print("\n📈 生成阻尼特性曲线...")
    
    system = create_hydrodynamic_damping_system(2.0, [0.5, 0.5, 0.3], 1.0)
    
    velocities = np.logspace(-2, 1, 50)  # 0.01 to 10 m/s
    damping_factors = []
    reynolds_numbers = []
    
    for v in velocities:
        lin_vel = np.array([v, 0.0, 0.0])
        ang_vel = np.array([0.0, 0.0, 0.0])
        
        result = system.calculate_hydrodynamic_damping(lin_vel, ang_vel)
        damping_factors.append(result['debug_info']['linear_damping_factor'])
        reynolds_numbers.append(result['debug_info']['reynolds_number'])
    
    # 绘图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # 阻尼因子 vs 速度
    ax1.semilogx(velocities, damping_factors, 'b-', linewidth=2)
    ax1.set_xlabel('速度 (m/s)')
    ax1.set_ylabel('阻尼因子')
    ax1.set_title('阻尼因子 vs 速度')
    ax1.grid(True, alpha=0.3)
    
    # 雷诺数 vs 速度
    ax2.loglog(velocities, reynolds_numbers, 'r-', linewidth=2)
    ax2.set_xlabel('速度 (m/s)')
    ax2.set_ylabel('雷诺数')
    ax2.set_title('雷诺数 vs 速度')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('damping_characteristics.png', dpi=300, bbox_inches='tight')
    print("  📊 特性曲线已保存为 damping_characteristics.png")

def main():
    """主测试函数"""
    print("🚀 水动力学阻尼系统优化验证测试")
    print("=" * 50)
    
    try:
        # 运行所有测试
        test_drag_coefficient_calculation()
        test_force_calculation_accuracy()
        test_numerical_stability()
        test_reynolds_number_calculation()
        
        # 生成特性曲线（需要matplotlib）
        try:
            plot_damping_characteristics()
        except ImportError:
            print("⚠️  matplotlib未安装，跳过特性曲线绘制")
        
        print("\n🎉 所有测试完成！")
        print("✅ 优化后的水动力学阻尼系统验证通过")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
