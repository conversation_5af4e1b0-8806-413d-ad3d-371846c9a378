import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D # 用于3D绘图
import math
from matplotlib.widgets import Cursor
import matplotlib.patches as patches

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# --- 配置 ---
DATA_FILE = "cog_simulation_data.npz" # 你的仿真脚本保存的文件名
SAVE_PLOTS = False  # 设置为True保存图片，False显示图片

def find_zero_crossings(data, time_axis):
    """
    找到数据与0轴的交点

    Args:
        data: 数据数组
        time_axis: 时间轴数组

    Returns:
        crossings: 交点的时间坐标列表
    """
    crossings = []
    for i in range(len(data) - 1):
        if (data[i] >= 0 and data[i+1] <= 0) or (data[i] <= 0 and data[i+1] >= 0):
            # 线性插值找到精确的交点
            if data[i+1] != data[i]:
                t_cross = time_axis[i] + (time_axis[i+1] - time_axis[i]) * (-data[i]) / (data[i+1] - data[i])
                crossings.append(t_cross)
    return crossings

def find_extrema(data, time_axis):
    """
    找到数据的极值点（最大值和最小值）

    Args:
        data: 数据数组
        time_axis: 时间轴数组

    Returns:
        extrema: [(time, value, type), ...] 其中type为'max'或'min'
    """
    extrema = []
    for i in range(1, len(data) - 1):
        if data[i] > data[i-1] and data[i] > data[i+1]:
            extrema.append((time_axis[i], data[i], 'max'))
        elif data[i] < data[i-1] and data[i] < data[i+1]:
            extrema.append((time_axis[i], data[i], 'min'))
    return extrema

def create_interactive_plot(time_axis, data, title, ylabel, color='blue'):
    """
    创建交互式图表，显示坐标和特殊点

    Args:
        time_axis: 时间轴数据
        data: Y轴数据
        title: 图表标题
        ylabel: Y轴标签
        color: 线条颜色

    Returns:
        fig, ax: matplotlib图形和轴对象
    """
    # 启用交互式模式
    plt.ion()

    fig, ax = plt.subplots(figsize=(12, 8))

    # 绘制主要数据线
    line, = ax.plot(time_axis, data, color=color, linewidth=2, label=title)

    # 添加网格
    ax.grid(True, alpha=0.3)

    # 找到零点交叉
    zero_crossings = find_zero_crossings(data, time_axis)

    # 找到极值点
    extrema = find_extrema(data, time_axis)

    # 标记零点交叉
    if zero_crossings:
        for crossing in zero_crossings:
            ax.axvline(x=crossing, color='red', linestyle='--', alpha=0.7)
            ax.plot(crossing, 0, 'ro', markersize=8, label='Zero Crossing' if crossing == zero_crossings[0] else "")
            ax.annotate(f'Zero: ({crossing:.2f}, 0.00)',
                       xy=(crossing, 0), xytext=(10, 10),
                       textcoords='offset points', fontsize=9,
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='red', alpha=0.7))

    # 标记极值点
    max_points = [(t, v) for t, v, type_ in extrema if type_ == 'max']
    min_points = [(t, v) for t, v, type_ in extrema if type_ == 'min']

    if max_points:
        max_times, max_values = zip(*max_points)
        ax.plot(max_times, max_values, 'g^', markersize=10, label='Local Max')
        for t, v in max_points:
            ax.annotate(f'Max: ({t:.2f}, {v:.4f})',
                       xy=(t, v), xytext=(10, 10),
                       textcoords='offset points', fontsize=9,
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='green', alpha=0.7))

    if min_points:
        min_times, min_values = zip(*min_points)
        ax.plot(min_times, min_values, 'rv', markersize=10, label='Local Min')
        for t, v in min_points:
            ax.annotate(f'Min: ({t:.2f}, {v:.4f})',
                       xy=(t, v), xytext=(10, -20),
                       textcoords='offset points', fontsize=9,
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='orange', alpha=0.7))

    # 添加十字光标
    cursor = Cursor(ax, useblit=True, color='black', linewidth=1)

    # 添加鼠标移动事件处理
    coord_text = ax.text(0.02, 0.98, '', transform=ax.transAxes, fontsize=12,
                        verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    def on_mouse_move(event):
        if event.inaxes == ax:
            coord_text.set_text(f'Coord: ({event.xdata:.2f}, {event.ydata:.6f})')
            fig.canvas.draw_idle()

    fig.canvas.mpl_connect('motion_notify_event', on_mouse_move)

    # 设置标签和标题
    ax.set_xlabel('Simulation Frame')
    ax.set_ylabel(ylabel)
    ax.set_title(title)
    ax.legend()

    # 添加统计信息
    stats_text = f'Statistics:\nMax: {np.max(data):.6f}\nMin: {np.min(data):.6f}\nMean: {np.mean(data):.6f}\nStd: {np.std(data):.6f}'
    ax.text(0.98, 0.98, stats_text, transform=ax.transAxes, fontsize=10,
            verticalalignment='top', horizontalalignment='right',
            bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

    plt.tight_layout()
    return fig, ax

def quaternion_to_euler(quaternions):
    """
    将四元数转换为欧拉角 (俯仰, 横滚, 偏航)

    Args:
        quaternions: Nx4 数组，每行为 [w, x, y, z] 四元数

    Returns:
        euler_angles: Nx3 数组，每行为 [pitch, roll, yaw] 弧度
    """
    if len(quaternions.shape) == 3 and quaternions.shape[1] == 1:
        quaternions = quaternions.squeeze(axis=1)

    euler_angles = np.zeros((quaternions.shape[0], 3))

    for i, q in enumerate(quaternions):
        w, x, y, z = q[0], q[1], q[2], q[3]

        # 俯仰角 (pitch) - 绕Y轴旋转
        sin_pitch = 2 * (w * y - z * x)
        sin_pitch = np.clip(sin_pitch, -1.0, 1.0)
        pitch = np.arcsin(sin_pitch)

        # 横滚角 (roll) - 绕X轴旋转
        sin_roll_cos_pitch = 2 * (w * x + y * z)
        cos_roll_cos_pitch = 1 - 2 * (x * x + y * y)
        roll = np.arctan2(sin_roll_cos_pitch, cos_roll_cos_pitch)

        # 偏航角 (yaw) - 绕Z轴旋转
        sin_yaw_cos_pitch = 2 * (w * z + x * y)
        cos_yaw_cos_pitch = 1 - 2 * (y * y + z * z)
        yaw = np.arctan2(sin_yaw_cos_pitch, cos_yaw_cos_pitch)

        euler_angles[i] = [pitch, roll, yaw]

    return euler_angles

def load_and_visualize_com_data(filename):
    """
    从NPZ文件加载重心（COM）数据、线性速度数据和四元数方向数据并进行可视化。
    文件预期包含 'sim_frame' (帧号)、'com_position' (Nx3 的重心坐标)、'liner_velocity' (Nx3 的线性速度)
    和 'quaternion_orientation' (Nx4 的四元数方向)。
    """
    try:
        data_loaded = np.load(filename)
        print(f"✅ 成功加载数据文件: {filename}")
        print("文件中包含的数组键值:", list(data_loaded.keys()))

        # 检查所需键是否存在
        required_keys = ['sim_frame', 'com_position']
        if not all(key in data_loaded for key in required_keys):
            missing_keys = [key for key in required_keys if key not in data_loaded]
            print(f"❌ 错误：数据文件 '{filename}' 缺少必要的键: {missing_keys}")
            return

        sim_frames = data_loaded['sim_frame']
        com_positions = data_loaded['com_position'] # 这应该是一个 (N, 3) 的数组

        # 检查是否有线性速度数据
        has_velocity_data = 'liner_velocity' in data_loaded
        if has_velocity_data:
            linear_velocities = data_loaded['liner_velocity']
            print("✅ 检测到线性速度数据")
        else:
            print("⚠️ 未检测到线性速度数据，将只显示COM位置")

        # 检查是否有四元数方向数据
        has_orientation_data = 'quaternion_orientation' in data_loaded
        if has_orientation_data:
            quaternion_orientations = data_loaded['quaternion_orientation']
            print("✅ 检测到四元数方向数据")
        else:
            print("⚠️ 未检测到四元数方向数据")

        if len(sim_frames) == 0 or com_positions.shape[0] == 0:
            print("⚠️ 数据文件中没有记录任何数据。")
            return

        print(f"COM 数据的形状: {com_positions.shape}")
        if has_velocity_data:
            print(f"线性速度数据的形状: {linear_velocities.shape}")
        if has_orientation_data:
            print(f"四元数方向数据的形状: {quaternion_orientations.shape}")

        # 检查数据维度是否正确并进行必要的重新整形
        if len(com_positions.shape) == 1:
            # 如果是1D数组，尝试重新整形
            if com_positions.shape[0] % 3 == 0:
                com_positions = com_positions.reshape(-1, 3)
                print(f"✅ 已将1D数组重新整形为: {com_positions.shape}")
            else:
                print(f"❌ 错误：COM数据维度不正确。期望 (N, 3) 或可整除为3的1D数组，但得到: {com_positions.shape}")
                return
        elif len(com_positions.shape) == 2:
            if com_positions.shape[1] == 1:
                print(f"❌ 错误：COM数据只有1列，期望3列 (X, Y, Z)。当前形状: {com_positions.shape}")
                print("这可能意味着数据保存时只记录了一个坐标分量。")
                return
            elif com_positions.shape[1] != 3:
                print(f"❌ 错误：COM数据应该有3列 (X, Y, Z)，但得到 {com_positions.shape[1]} 列。")
                return
        elif len(com_positions.shape) == 3:
            # 处理3D数组的情况，例如 (N, 1, 3) -> (N, 3)
            if com_positions.shape[1] == 1 and com_positions.shape[2] == 3:
                com_positions = com_positions.squeeze(axis=1)  # 移除中间的维度
                print(f"✅ 已将3D数组 {com_positions.shape} 压缩为2D数组: {com_positions.shape}")
            elif com_positions.shape[2] == 3:
                # 如果最后一个维度是3，尝试重新整形
                com_positions = com_positions.reshape(-1, 3)
                print(f"✅ 已将3D数组重新整形为: {com_positions.shape}")
            else:
                print(f"❌ 错误：3D COM数据的最后一个维度应该是3 (X, Y, Z)，但得到: {com_positions.shape}")
                return
        else:
            print(f"❌ 错误：COM数据维度不正确。期望1D、2D或3D数组，但得到: {com_positions.shape}")
            return

        # 处理线性速度数据的维度（如果存在）
        if has_velocity_data:
            # 对线性速度数据应用相同的维度处理逻辑
            if len(linear_velocities.shape) == 3:
                if linear_velocities.shape[1] == 1 and linear_velocities.shape[2] == 3:
                    linear_velocities = linear_velocities.squeeze(axis=1)
                    print(f"✅ 已将线性速度3D数组压缩为2D数组: {linear_velocities.shape}")
                elif linear_velocities.shape[2] == 3:
                    linear_velocities = linear_velocities.reshape(-1, 3)
                    print(f"✅ 已将线性速度3D数组重新整形为: {linear_velocities.shape}")
            elif len(linear_velocities.shape) == 2 and linear_velocities.shape[1] != 3:
                print(f"❌ 错误：线性速度数据应该有3列 (Vx, Vy, Vz)，但得到 {linear_velocities.shape[1]} 列。")
                has_velocity_data = False
            elif len(linear_velocities.shape) == 1:
                if linear_velocities.shape[0] % 3 == 0:
                    linear_velocities = linear_velocities.reshape(-1, 3)
                    print(f"✅ 已将线性速度1D数组重新整形为: {linear_velocities.shape}")
                else:
                    print(f"❌ 错误：线性速度数据维度不正确: {linear_velocities.shape}")
                    has_velocity_data = False

        # 处理四元数方向数据的维度（如果存在）
        if has_orientation_data:
            # 对四元数数据应用维度处理逻辑
            if len(quaternion_orientations.shape) == 3:
                if quaternion_orientations.shape[1] == 1 and quaternion_orientations.shape[2] == 4:
                    quaternion_orientations = quaternion_orientations.squeeze(axis=1)
                    print(f"✅ 已将四元数3D数组压缩为2D数组: {quaternion_orientations.shape}")
                elif quaternion_orientations.shape[2] == 4:
                    quaternion_orientations = quaternion_orientations.reshape(-1, 4)
                    print(f"✅ 已将四元数3D数组重新整形为: {quaternion_orientations.shape}")
            elif len(quaternion_orientations.shape) == 2 and quaternion_orientations.shape[1] != 4:
                print(f"❌ 错误：四元数数据应该有4列 (w, x, y, z)，但得到 {quaternion_orientations.shape[1]} 列。")
                has_orientation_data = False
            elif len(quaternion_orientations.shape) == 1:
                if quaternion_orientations.shape[0] % 4 == 0:
                    quaternion_orientations = quaternion_orientations.reshape(-1, 4)
                    print(f"✅ 已将四元数1D数组重新整形为: {quaternion_orientations.shape}")
                else:
                    print(f"❌ 错误：四元数数据维度不正确: {quaternion_orientations.shape}")
                    has_orientation_data = False

        # 提取 COM 的 X, Y, Z 分量
        com_x = com_positions[:, 0]
        com_y = com_positions[:, 1]
        com_z = com_positions[:, 2]

        # 提取线性速度的 X, Y, Z 分量（如果存在）
        if has_velocity_data:
            vel_x = linear_velocities[:, 0]
            vel_y = linear_velocities[:, 1]
            vel_z = linear_velocities[:, 2]

        # 处理四元数数据并转换为欧拉角（如果存在）
        if has_orientation_data:
            # 转换四元数为欧拉角
            euler_angles = quaternion_to_euler(quaternion_orientations)
            pitch = euler_angles[:, 0] * 180.0 / np.pi  # 转换为度
            roll = euler_angles[:, 1] * 180.0 / np.pi
            yaw = euler_angles[:, 2] * 180.0 / np.pi

            print(f"欧拉角范围:")
            print(f"  俯仰角(Pitch): [{np.min(pitch):.2f}°, {np.max(pitch):.2f}°]")
            print(f"  横滚角(Roll): [{np.min(roll):.2f}°, {np.max(roll):.2f}°]")
            print(f"  偏航角(Yaw): [{np.min(yaw):.2f}°, {np.max(yaw):.2f}°]")

        # 假设时间轴就是帧号，或者您可以根据物理步长计算时间
        # 例如：time = sim_frames * physics_dt
        time_axis = sim_frames

        print(f"记录的帧数: {len(sim_frames)}")


        # --- 1. COM位置、线性速度和方向组合图 ---
        if has_velocity_data or has_orientation_data:
            # 检查数据变化范围
            com_x_range = np.max(com_x) - np.min(com_x)
            com_y_range = np.max(com_y) - np.min(com_y)
            com_z_range = np.max(com_z) - np.min(com_z)

            print(f"数据变化范围分析:")
            print(f"  COM: X={com_x_range:.4f}m, Y={com_y_range:.4f}m, Z={com_z_range:.4f}m")

            if has_velocity_data:
                vel_x_range = np.max(vel_x) - np.min(vel_x)
                vel_y_range = np.max(vel_y) - np.min(vel_y)
                vel_z_range = np.max(vel_z) - np.min(vel_z)
                print(f"  速度: X={vel_x_range:.4f}m/s, Y={vel_y_range:.4f}m/s, Z={vel_z_range:.4f}m/s")

            if has_orientation_data:
                pitch_range = np.max(pitch) - np.min(pitch)
                roll_range = np.max(roll) - np.min(roll)
                yaw_range = np.max(yaw) - np.min(yaw)
                print(f"  姿态: Pitch={pitch_range:.2f}°, Roll={roll_range:.2f}°, Yaw={yaw_range:.2f}°")

            # 确定子图数量
            num_data_types = 1  # COM位置
            if has_velocity_data:
                num_data_types += 1
            if has_orientation_data:
                num_data_types += 1

            # 创建详细的多子图布局
            fig = plt.figure(figsize=(16, 4 * num_data_types))

            # 动态创建子图
            subplot_idx = 1

            # 1. COM位置 - 分别显示每个轴
            ax1 = plt.subplot(num_data_types, 3, subplot_idx)
            plt.plot(time_axis, com_x, 'r-', linewidth=2, label='COM X')
            plt.ylabel('X Position (m)')
            plt.title(f'COM X Position (Range: {com_x_range:.4f}m)')
            plt.grid(True)
            plt.legend()

            ax2 = plt.subplot(num_data_types, 3, subplot_idx + 1)
            plt.plot(time_axis, com_y, 'g-', linewidth=2, label='COM Y')
            plt.ylabel('Y Position (m)')
            plt.title(f'COM Y Position (Range: {com_y_range:.4f}m)')
            plt.grid(True)
            plt.legend()

            ax3 = plt.subplot(num_data_types, 3, subplot_idx + 2)
            plt.plot(time_axis, com_z, 'b-', linewidth=2, label='COM Z')
            plt.ylabel('Z Position (m)')
            plt.title(f'COM Z Position (Range: {com_z_range:.4f}m)')
            plt.grid(True)
            plt.legend()

            subplot_idx += 3

            # 2. 线性速度 - 分别显示每个轴（如果存在）
            if has_velocity_data:
                ax4 = plt.subplot(num_data_types, 3, subplot_idx)
                plt.plot(time_axis, vel_x, 'r--', linewidth=2, alpha=0.8, label='Velocity X')
                plt.ylabel('X Velocity (m/s)')
                plt.title(f'Linear Velocity X (Range: {vel_x_range:.4f}m/s)')
                plt.grid(True)
                plt.legend()

                ax5 = plt.subplot(num_data_types, 3, subplot_idx + 1)
                plt.plot(time_axis, vel_y, 'g--', linewidth=2, alpha=0.8, label='Velocity Y')
                plt.ylabel('Y Velocity (m/s)')
                plt.title(f'Linear Velocity Y (Range: {vel_y_range:.4f}m/s)')
                plt.grid(True)
                plt.legend()

                ax6 = plt.subplot(num_data_types, 3, subplot_idx + 2)
                plt.plot(time_axis, vel_z, 'b--', linewidth=2, alpha=0.8, label='Velocity Z')
                plt.ylabel('Z Velocity (m/s)')
                plt.title(f'Linear Velocity Z (Range: {vel_z_range:.4f}m/s)')
                plt.grid(True)
                plt.legend()

                subplot_idx += 3

            # 3. 欧拉角 - 分别显示每个轴（如果存在）
            if has_orientation_data:
                ax7 = plt.subplot(num_data_types, 3, subplot_idx)
                plt.plot(time_axis, pitch, 'purple', linewidth=2, label='Pitch')
                plt.ylabel('Pitch (degrees)')
                plt.title(f'Pitch Angle (Range: {pitch_range:.2f}°)')
                plt.grid(True)
                plt.legend()

                ax8 = plt.subplot(num_data_types, 3, subplot_idx + 1)
                plt.plot(time_axis, roll, 'orange', linewidth=2, label='Roll')
                plt.ylabel('Roll (degrees)')
                plt.title(f'Roll Angle (Range: {roll_range:.2f}°)')
                plt.grid(True)
                plt.legend()

                ax9 = plt.subplot(num_data_types, 3, subplot_idx + 2)
                plt.plot(time_axis, yaw, 'brown', linewidth=2, label='Yaw')
                plt.ylabel('Yaw (degrees)')
                plt.title(f'Yaw Angle (Range: {yaw_range:.2f}°)')
                plt.grid(True)
                plt.legend()

            # 为最后一行添加X轴标签
            for i in range(3):
                ax = plt.subplot(num_data_types, 3, (num_data_types-1)*3 + i + 1)
                ax.set_xlabel('Simulation Frame')

            plt.tight_layout()

            if SAVE_PLOTS:
                plt.savefig('com_and_velocity_detailed.png', dpi=300, bbox_inches='tight')
                print("✅ 详细COM位置和速度图已保存为 'com_and_velocity_detailed.png'")

            # 创建传统组合图
            num_subplots = 1  # COM位置
            if has_velocity_data:
                num_subplots += 1
            if has_orientation_data:
                num_subplots += 1

            fig2, axes = plt.subplots(num_subplots, 1, figsize=(14, 6 * num_subplots))
            if num_subplots == 1:
                axes = [axes]  # 确保axes是列表

            subplot_idx = 0

            # COM位置子图
            axes[subplot_idx].plot(time_axis, com_x, label='COM X', linestyle='-', color='red', linewidth=2)
            axes[subplot_idx].plot(time_axis, com_y, label='COM Y', linestyle='-', color='green', linewidth=2)
            axes[subplot_idx].plot(time_axis, com_z, label='COM Z', linestyle='-', color='blue', linewidth=2)
            axes[subplot_idx].set_ylabel('Position (m)')
            axes[subplot_idx].set_title('Center of Mass (COM) Position Over Simulation Frames')
            axes[subplot_idx].grid(True)
            axes[subplot_idx].legend()
            subplot_idx += 1

            # 线性速度子图（如果存在）
            if has_velocity_data:
                axes[subplot_idx].plot(time_axis, vel_x, label='Velocity X', linestyle='--', color='red', alpha=0.8, linewidth=2)
                axes[subplot_idx].plot(time_axis, vel_y, label='Velocity Y', linestyle='--', color='green', alpha=0.8, linewidth=2)
                axes[subplot_idx].plot(time_axis, vel_z, label='Velocity Z', linestyle='--', color='blue', alpha=0.8, linewidth=2)
                axes[subplot_idx].set_ylabel('Velocity (m/s)')
                axes[subplot_idx].set_title('Linear Velocity Over Simulation Frames')
                axes[subplot_idx].grid(True)
                axes[subplot_idx].legend()
                subplot_idx += 1

            # 欧拉角子图（如果存在）
            if has_orientation_data:
                axes[subplot_idx].plot(time_axis, pitch, label='Pitch', linestyle=':', color='purple', linewidth=2)
                axes[subplot_idx].plot(time_axis, roll, label='Roll', linestyle=':', color='orange', linewidth=2)
                axes[subplot_idx].plot(time_axis, yaw, label='Yaw', linestyle=':', color='brown', linewidth=2)
                axes[subplot_idx].set_ylabel('Angle (degrees)')
                axes[subplot_idx].set_title('Euler Angles (Orientation) Over Simulation Frames')
                axes[subplot_idx].grid(True)
                axes[subplot_idx].legend()

            # 为最后一个子图添加X轴标签
            axes[-1].set_xlabel('Simulation Frame')

            plt.tight_layout()

            if SAVE_PLOTS:
                plt.savefig('com_velocity_orientation_combined.png', dpi=300, bbox_inches='tight')
                print("✅ COM位置、速度和方向组合图已保存为 'com_velocity_orientation_combined.png'")
        else:
            # 如果没有速度数据，只显示COM位置
            plt.figure(figsize=(12, 6))
            plt.plot(time_axis, com_x, label='COM X', linestyle='-')
            plt.plot(time_axis, com_y, label='COM Y', linestyle='-')
            plt.plot(time_axis, com_z, label='COM Z', linestyle='-')

            plt.xlabel('Simulation Frame')
            plt.ylabel('Coordinate (m)')
            plt.title('Center of Mass (COM) Position Over Simulation Frames')
            plt.grid(True)
            plt.legend()
            plt.tight_layout()

            if SAVE_PLOTS:
                plt.savefig('com_trajectory_2d.png', dpi=300, bbox_inches='tight')
                print("✅ 2D轨迹图已保存为 'com_trajectory_2d.png'")

        # --- 2. COM 3D 轨迹图（带速度向量可视化） ---
        if has_velocity_data:
            fig = plt.figure(figsize=(15, 6))

            # 左侧：3D COM轨迹
            ax_3d1 = fig.add_subplot(121, projection='3d')
            ax_3d1.plot(com_x, com_y, com_z,
                       label='COM Path', color='blue', marker='o', markersize=2, alpha=0.8)

            # 标记起始和结束点
            if len(com_x) > 1:
                ax_3d1.scatter(com_x[0], com_y[0], com_z[0],
                              color='green', s=100, label='Start', marker='^')
                ax_3d1.scatter(com_x[-1], com_y[-1], com_z[-1],
                              color='red', s=100, label='End', marker='s')

            ax_3d1.set_xlabel('X (m)')
            ax_3d1.set_ylabel('Y (m)')
            ax_3d1.set_zlabel('Z (m)')
            ax_3d1.set_title('3D COM Trajectory')
            ax_3d1.grid(True)
            ax_3d1.legend()
            ax_3d1.view_init(elev=20, azim=-60)

            # 右侧：3D速度向量轨迹
            ax_3d2 = fig.add_subplot(122, projection='3d')
            ax_3d2.plot(vel_x, vel_y, vel_z,
                       label='Velocity Path', color='orange', marker='s', markersize=2, alpha=0.8)

            # 标记速度向量的起始和结束点
            if len(vel_x) > 1:
                ax_3d2.scatter(vel_x[0], vel_y[0], vel_z[0],
                              color='green', s=100, label='Start Vel', marker='^')
                ax_3d2.scatter(vel_x[-1], vel_y[-1], vel_z[-1],
                              color='red', s=100, label='End Vel', marker='s')

            ax_3d2.set_xlabel('Vx (m/s)')
            ax_3d2.set_ylabel('Vy (m/s)')
            ax_3d2.set_zlabel('Vz (m/s)')
            ax_3d2.set_title('3D Velocity Vector Trajectory')
            ax_3d2.grid(True)
            ax_3d2.legend()
            ax_3d2.view_init(elev=20, azim=-60)

            plt.tight_layout()

            if SAVE_PLOTS:
                plt.savefig('com_and_velocity_3d.png', dpi=300, bbox_inches='tight')
                print("✅ COM和速度3D轨迹图已保存为 'com_and_velocity_3d.png'")
        else:
            # 如果没有速度数据，只显示COM的3D轨迹
            fig = plt.figure(figsize=(10, 8))
            ax_3d = fig.add_subplot(111, projection='3d')

            ax_3d.plot(com_x, com_y, com_z,
                       label='COM Path', color='blue', marker='o', markersize=3, alpha=0.8)

            # 可选：标记起始和结束点
            if len(com_x) > 1:
                ax_3d.scatter(com_x[0], com_y[0], com_z[0],
                              color='green', s=100, label='Start COM', marker='^')
                ax_3d.scatter(com_x[-1], com_y[-1], com_z[-1],
                              color='red', s=100, label='End COM', marker='s')

            ax_3d.set_xlabel('X (m)')
            ax_3d.set_ylabel('Y (m)')
            ax_3d.set_zlabel('Z (m)')
            ax_3d.set_title('3D Path of Center of Mass (COM)')
            ax_3d.grid(True)
            ax_3d.legend()
            ax_3d.view_init(elev=20, azim=-60)

            if SAVE_PLOTS:
                plt.savefig('com_trajectory_3d.png', dpi=300, bbox_inches='tight')
                print("✅ 3D轨迹图已保存为 'com_trajectory_3d.png'")

        # --- 3. 创建COM Z的单独交互式图表 ---
        print("\n🎯 创建COM Z的交互式图表...")
        fig_com_z, ax_com_z = create_interactive_plot(
            time_axis, com_z,
            'COM Z Position - Interactive View',
            'Z Position (m)',
            'blue'
        )

        if SAVE_PLOTS:
            fig_com_z.savefig('com_z_interactive.png', dpi=300, bbox_inches='tight')
            print("✅ COM Z交互式图表已保存为 'com_z_interactive.png'")

        # --- 4. 创建Linear Velocity Z的单独交互式图表（如果存在） ---
        if has_velocity_data:
            print("\n🎯 创建Linear Velocity Z的交互式图表...")
            fig_vel_z, ax_vel_z = create_interactive_plot(
                time_axis, vel_z,
                'Linear Velocity Z - Interactive View',
                'Z Velocity (m/s)',
                'red'
            )

            if SAVE_PLOTS:
                fig_vel_z.savefig('velocity_z_interactive.png', dpi=300, bbox_inches='tight')
                print("✅ Linear Velocity Z交互式图表已保存为 'velocity_z_interactive.png'")

        if not SAVE_PLOTS:
            plt.show()

    except FileNotFoundError:
        print(f"❌ 错误：未找到数据文件 '{filename}'。请确保仿真脚本已运行并生成了该文件。")
    except Exception as e:
        print(f"❌ 加载或可视化数据时发生错误: {e}: {e.__class__.__name__}")
        import traceback
        traceback.print_exc() # 打印完整的错误栈信息

if __name__ == "__main__":
    load_and_visualize_com_data(DATA_FILE)