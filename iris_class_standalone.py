#!/usr/bin/env python3
"""
无人机水下体积实时计算 - Standalone模式

此脚本演示了如何以结构化的方式在Isaac Sim中实时计算
一个复杂关节机器人（如无人机）特定部位浸入水中的体积。

- 目标环境: ground_water.usd
- 目标对象: /World/iris (无人机)
- 核心计算: 实时切分碰撞网格，计算水下部分的体积
"""

# Isaac Sim标准初始化代码
from isaacsim import SimulationApp
simulation_app = SimulationApp({"headless": False})

import os
import numpy as np
import carb
import omni.usd
from isaacsim.core.api import World                    # Isaac Sim世界管理器
from isaacsim.core.prims import Articulation,RigidPrim
from isaacsim.core.utils.stage import add_reference_to_stage
from isaacsim.core.utils.prims import get_prim_at_path # 获取场景对象的工具函数
import isaacsim.core.utils.mesh as mesh_utils
import isaacsim.core.utils.stage as stage_utils
from utils_calculate.geometry_utils_volume import slice_convex_poly_volume

class IrisBuoyancySim:
    """
    无人机浮力仿真主类
    """
    def __init__(self):
        """初始化仿真环境状态"""
        self.world = None
        self.iris_robot = None
        self.target_collision_prim = None
        self.coord_prim = None
        # 添加ArticulationPrim，可以获取到世界Pose 
        self.robot_Articulation_prim = None
        self.last_volume_data = None
        self.time_flag = 1

        self.world_vertices_record = None
        # 默认配置参数
        self.config = {
            'water_surface_z': 0,          # 水面高度 (Z轴)
            'water_plane_normal': (0, 0, 1), # 水平面法线
            'robot_prim_path': "/World/iris",
            'collision_body_rel_path': "base_link/collisions", # 要计算体积的碰撞体
            'debug_mode': True,
        }

        self.coord_prim_path = "/World/world_point"

        # 调试和性能控制
        self.sim_frame_count = 0
        self.debug_interval = 1  # 每30帧输出一次调试信息

    def apply_config(self, config_dict):
        """应用外部配置参数到仿真实例"""
        self.config.update(config_dict)
        print(f"✅ 配置已应用: {len(config_dict)} 个参数")

    def setup_scene(self, environment_usd_path: str, robot_usd_path: str):
        """设置仿真世界、加载环境和机器人"""
        print("🌊 开始设置场景...")

        # 1. 加载环境USD
        if not os.path.exists(environment_usd_path):
            carb.log_error(f"❌ 环境USD文件不存在: {environment_usd_path}")
            return False
        
        omni.usd.get_context().open_stage(environment_usd_path)
        # 更改渲染步长和物理步长
        # 2. 创建并初始化World对象
        self.world = World(
            physics_dt = 1.0 / 120.0,
            rendering_dt = 1.0 / 120.0,
            stage_units_in_meters=1.0)

        self.world.initialize_physics()
        # 我们可以选择禁用默认重力，如果后续要手动计算浮力+重力的话
        # self.world.get_physics_context().set_gravity(0.0)

        # 3. 加载机器人USD
        if not os.path.exists(robot_usd_path):
            carb.log_error(f"❌ 机器人USD文件不存在: {robot_usd_path}")
            return False
        
        add_reference_to_stage(
            usd_path=robot_usd_path, 
            prim_path=self.config['robot_prim_path']
        )

        # 4. 创建机器人仿真对象
        robot_path = self.config['robot_prim_path']
        self.iris_robot = self.world.scene.add(
            Articulation(
                prim_paths_expr=robot_path, 
                name="iris"
            )
        )
        print(f"✅ 关节机器人 '{robot_path}' 已添加")

        self.robot_Articulation_prim = self.world.scene.get_object("iris")
        print(f"  - 机器人世界坐标: {self.robot_Articulation_prim.get_world_poses()}")
        # 5. 获取用于计算体积的目标碰撞体Prim
        collision_prim_path = f"{robot_path}/{self.config['collision_body_rel_path']}"
        self.target_collision_prim = stage_utils.get_current_stage().GetPrimAtPath(collision_prim_path)
        


        if not self.target_collision_prim.IsValid():
            carb.log_error(f"❌ 无法找到目标碰撞体: {collision_prim_path}")
            return False
        
        
        print(f"✅ 目标碰撞体已锁定: {collision_prim_path}")
        

        # 5.1 创建参考点
        self.coord_prim  = stage_utils.get_current_stage().GetPrimAtPath(self.coord_prim_path)


        # 6. 重置世界，让所有对象初始化
        self.world.reset()

            # 7. 注册物理回调！
        self.world.add_physics_callback(
            "iris_volume_calculator",
            callback_fn=self._volume_calculation_callback
        )
        print("✅ 物理回调函数 'iris_volume_calculator' 已注册")
        # 运行几帧让物理稳定
        # 运行几帧让物理稳定
        for _ in range(5):
            simulation_app.update()
            
        print("🎉 场景设置成功!")
        return True

    def _volume_calculation_callback(self, step_size):
        """
        物理回调函数。此函数会在每个物理步进后自动执行。
        """
        print("-----------------------------------------------------------------进入回调函数")
        if not self.target_collision_prim:
            self.last_volume_data = None
            return
        try:
            world_vertices = mesh_utils.get_mesh_vertices_relative_to(
                self.target_collision_prim, self.coord_prim
            )
            self.world_vertices_record = world_vertices
            print("world_vertices_in_callbacks:\n",world_vertices)
            total, below, above = slice_convex_poly_volume(
                world_vertices,
                plane_normal=self.config['water_plane_normal'],
                plane_d=self.config['water_surface_z']
            )
            
            self.time_flag += 1
            self.last_volume_data = {
                'total_volume': total,
                'submerged_volume': below,
                'exposed_volume': above,
                'time_flag': self.time_flag,
                'world_vertices': world_vertices
            }
            print(f"💧 水下体积计算结果:")
            print(f"   - 总 体 积: {self.last_volume_data['total_volume']:.6f} m³")
            print(f"   - 水下体积: {self.last_volume_data['submerged_volume']:.6f} m³")
            print(f"   - 水上体积: {self.last_volume_data['exposed_volume']:.6f} m³")
            print(f"   - 时间戳: {self.last_volume_data['time_flag']}")
            print(f"   - 世界坐标: {self.last_volume_data['world_vertices']}")
            # 1. 获取机器人状态（可选，未来可用于施加力）
            self.base_link_pose = {'position': self.iris_robot.get_world_poses(),
                                   'time_flag': self.time_flag}
            # 这里只是个示例，实际中你会获取base_link的世界姿态
            # print(f"🤖 机器人状态: {base_link_pose}")
        except Exception as e:
            carb.log_error(f"❌ 体积计算回调失败: {e}")
            self.last_volume_data = None

    def step(self):
        """执行单步仿真逻辑"""
        # 步进物理仿真和渲染
        self.world.step(render=True)
        self.sim_frame_count += 1

        # 按固定频率执行计算和打印
        # if self.sim_frame_count % self.debug_interval == 0:
        print(f"\n--- [帧 {self.sim_frame_count}] ---")
            
            # 1. 获取机器人状态（可选，未来可用于施加力）
            # base_link_pose = self.iris_robot.get_joint_positions()
            # 这里只是个示例，实际中你会获取base_link的世界姿态
            # print(f"🤖 机器人状态: {self.base_link_pose}")

            # 2. 计算浸水体积
            # volume_data = self.calculate_submerged_volume()
             
            # if self.last_volume_data:
            #     print(f"💧 水下体积计算结果:")
            #     print(f"   - 总 体 积: {self.last_volume_data['total_volume']:.6f} m³")
            #     print(f"   - 水下体积: {self.last_volume_data['submerged_volume']:.6f} m³")
            #     print(f"   - 水上体积: {self.last_volume_data['exposed_volume']:.6f} m³")
            #     print(f"   - 时间戳: {self.last_volume_data['time_flag']}")
            #     print(f"   - 世界坐标: {self.last_volume_data['world_vertices']}")
            # 3. 在这里可以根据水下体积计算并应用浮力
            # buoyancy_force = volume_data['submerged_volume'] * WATER_DENSITY * GRAVITY
            # self.iris_robot.get_body("base_link").apply_force(...)

def main():
    """主函数 - 统一参数配置和仿真流程控制"""

    # ========================================
    # 🎛️ 统一参数配置区域
    # ========================================
    
    # 📁 文件路径配置
    ENV_USD_PATH = "/home/<USER>/Learn_standalone_isaac/usd_assets/ground_water.usd"
    ROBOT_USD_PATH = "/home/<USER>/Learn_standalone_isaac/usd_assets/iris_fov.usd"
    
    # 🌊 水环境参数
    WATER_SURFACE_Z = 0.0  # 水面高度为 Z=0.5 米

    # ========================================
    # 🚀 仿真初始化和运行
    # ========================================

    sim = IrisBuoyancySim()

    # 应用配置
    config_to_apply = {
        'water_surface_z': WATER_SURFACE_Z
    }
    sim.apply_config(config_to_apply)

    # 设置场景
    if not sim.setup_scene(environment_usd_path=ENV_USD_PATH, robot_usd_path=ROBOT_USD_PATH):
        carb.log_error("❌ 场景设置失败，程序退出。")
        simulation_app.close()
        return

    print("\n🎉 仿真设置完成，开始实时计算循环...")

    try:
        while simulation_app.is_running():
            sim.step()
    except KeyboardInterrupt:
        print("\n用户手动中断仿真。")
    except Exception as e:
        carb.log_error(f"❌ 仿真循环中出现严重错误: {e}")
    finally:
        print("关闭仿真程序。")
        simulation_app.close()

if __name__ == "__main__":
    main()
