"""
稳定阻尼系统 - 专门解决震荡问题
Stable Damping System - Specifically designed to eliminate oscillations

基于控制理论和流体力学的稳定阻尼设计
"""

import numpy as np
from typing import Dict, Tuple, Optional
import math

class StableDampingSystem:
    def __init__(self, mass: float, inertia: list, characteristic_length: float):
        """
        初始化稳定阻尼系统
        
        Args:
            mass: 物体质量 (kg)
            inertia: 对角惯性矩 [Ixx, Iyy, Izz] (kg·m²)
            characteristic_length: 特征长度 (m)
        """
        self.mass = mass
        self.inertia = np.array(inertia)
        self.char_length = characteristic_length
        
        # 🎯 基于临界阻尼理论计算稳定阻尼
        self.critical_damping_linear = self._calculate_critical_linear_damping()
        self.critical_damping_angular = self._calculate_critical_angular_damping()
        
        # 🛡️ 稳定性优先的阻尼系数（轻微过阻尼）
        self.stable_linear_damping = self.critical_damping_linear * 1.2  # 20%过阻尼
        self.stable_angular_damping = self.critical_damping_angular * 0.6  # 🎯 进一步减少角阻尼，提高响应性
        
        # 📊 平滑调整参数
        self.velocity_history = []
        self.angular_velocity_history = []
        self.damping_history = []
        self.history_length = 20  # 更长的历史记录用于平滑
        
        # 🔧 平滑调整配置
        self.smooth_config = {
            'velocity_smoothing_factor': 0.1,    # 速度平滑因子（越小越平滑）
            'damping_adjustment_rate': 0.05,     # 阻尼调整速率（越小变化越缓慢）
            'max_damping_multiplier': 1.5,       # 最大阻尼倍数（保守）
            'min_damping_multiplier': 0.8,       # 最小阻尼倍数（保守）
            'velocity_threshold': 0.1,           # 速度阈值，低于此值不调整
            'angular_velocity_threshold': 0.05,  # 角速度阈值

            # 🎯 角度响应优化参数
            'angular_smoothing_factor': 0.2,     # 角速度平滑因子（比线性更敏感）
            'angular_adjustment_rate': 0.15,     # 角阻尼调整速率（更快响应）
            'angular_max_multiplier': 2.0,       # 角阻尼最大倍数（更大范围）
            'angular_min_multiplier': 0.5,       # 角阻尼最小倍数（更小最小值）
        }
        
        print(f"🛡️ 稳定阻尼系统初始化完成")
        print(f"  临界线性阻尼: {self.critical_damping_linear:.2f} N·s/m")
        print(f"  稳定线性阻尼: {self.stable_linear_damping:.2f} N·s/m")
        print(f"  临界角阻尼: {self.critical_damping_angular:.2f} N·m·s/rad")
        print(f"  稳定角阻尼: {self.stable_angular_damping:.2f} N·m·s/rad")
    
    def _calculate_critical_linear_damping(self) -> float:
        """计算临界线性阻尼系数"""
        # 基于质量-弹簧-阻尼系统的临界阻尼
        # 假设"弹簧常数"来自浮力恢复力
        fluid_density = 1000.0  # 水密度
        volume_estimate = self.mass / 800.0  # 假设物体密度约800 kg/m³
        buoyancy_stiffness = fluid_density * 9.81 * volume_estimate / 0.1  # 假设0.1m位移
        
        # 临界阻尼: c = 2*sqrt(k*m)
        critical_damping = 2.0 * math.sqrt(buoyancy_stiffness * self.mass)
        
        # 确保最小值（基于经验）
        min_damping = self.mass * 15.0  # 每kg至少15 N·s/m
        
        return max(critical_damping, min_damping)
    
    def _calculate_critical_angular_damping(self) -> float:
        """计算临界角阻尼系数"""
        avg_inertia = np.mean(self.inertia)
        
        # 基于转动惯量的角阻尼
        # 假设角度恢复力矩来自浮力力矩
        angular_stiffness = self.mass * 9.81 * (self.char_length / 2.0)  # 估算
        critical_angular_damping = 2.0 * math.sqrt(angular_stiffness * avg_inertia)
        
        # 确保最小值
        min_angular_damping = avg_inertia * 50.0  # 每kg·m²至少50 N·m·s/rad
        
        return max(critical_angular_damping, min_angular_damping)
    
    def _smooth_velocity(self, current_velocity: np.ndarray) -> np.ndarray:
        """平滑速度信号，减少噪声影响"""
        if len(self.velocity_history) == 0:
            return current_velocity
        
        # 指数移动平均
        alpha = self.smooth_config['velocity_smoothing_factor']
        smoothed = alpha * current_velocity + (1 - alpha) * self.velocity_history[-1]
        return smoothed
    
    def _smooth_angular_velocity(self, current_angular_velocity: np.ndarray) -> np.ndarray:
        """平滑角速度信号（使用专门的角速度平滑参数）"""
        if len(self.angular_velocity_history) == 0:
            return current_angular_velocity

        # 🎯 使用专门的角速度平滑因子，提高响应性
        alpha = self.smooth_config['angular_smoothing_factor']
        smoothed = alpha * current_angular_velocity + (1 - alpha) * self.angular_velocity_history[-1]
        return smoothed
    
    def _calculate_gentle_adjustment(self, velocity_magnitude: float,
                                   is_angular: bool = False) -> float:
        """计算温和的阻尼调整因子"""
        threshold = (self.smooth_config['angular_velocity_threshold'] if is_angular
                    else self.smooth_config['velocity_threshold'])

        if velocity_magnitude < threshold:
            return 1.0  # 低速时不调整

        # 🎯 角度和线性使用不同的调整参数
        if is_angular:
            max_mult = self.smooth_config['angular_max_multiplier']
            min_mult = self.smooth_config['angular_min_multiplier']
        else:
            max_mult = self.smooth_config['max_damping_multiplier']
            min_mult = self.smooth_config['min_damping_multiplier']

        # 使用对数函数实现平缓调整
        log_factor = math.log(1 + velocity_magnitude) / math.log(1 + 2.0)  # 归一化到[0,1]
        adjustment = min_mult + (max_mult - min_mult) * min(log_factor, 1.0)

        return adjustment
    
    def calculate_stable_damping(self, linear_velocity: np.ndarray, 
                               angular_velocity: np.ndarray) -> Dict[str, np.ndarray]:
        """
        计算稳定的阻尼系数
        
        Args:
            linear_velocity: 当前线速度 [vx, vy, vz] (m/s)
            angular_velocity: 当前角速度 [wx, wy, wz] (rad/s)
            
        Returns:
            Dict包含阻尼系数和调试信息
        """
        # 平滑速度信号
        smooth_linear_vel = self._smooth_velocity(linear_velocity)
        smooth_angular_vel = self._smooth_angular_velocity(angular_velocity)
        
        # 更新历史记录
        self.velocity_history.append(smooth_linear_vel.copy())
        self.angular_velocity_history.append(smooth_angular_vel.copy())
        
        if len(self.velocity_history) > self.history_length:
            self.velocity_history.pop(0)
        if len(self.angular_velocity_history) > self.history_length:
            self.angular_velocity_history.pop(0)
        
        # 计算速度大小
        linear_speed = np.linalg.norm(smooth_linear_vel)
        angular_speed = np.linalg.norm(smooth_angular_vel)
        
        # 计算温和的调整因子
        linear_adjustment = self._calculate_gentle_adjustment(linear_speed, False)
        angular_adjustment = self._calculate_gentle_adjustment(angular_speed, True)
        
        # 如果有历史阻尼记录，进行平滑过渡
        if self.damping_history:
            last_linear_adj, last_angular_adj = self.damping_history[-1]

            # 🎯 线性和角度使用不同的调整速率
            linear_rate = self.smooth_config['damping_adjustment_rate']
            angular_rate = self.smooth_config['angular_adjustment_rate']

            # 指数移动平均平滑阻尼调整
            linear_adjustment = (linear_rate * linear_adjustment +
                               (1 - linear_rate) * last_linear_adj)
            angular_adjustment = (angular_rate * angular_adjustment +
                                (1 - angular_rate) * last_angular_adj)
        
        # 记录当前调整因子
        self.damping_history.append((linear_adjustment, angular_adjustment))
        if len(self.damping_history) > self.history_length:
            self.damping_history.pop(0)
        
        # 计算最终阻尼系数
        linear_damping = np.array([
            self.stable_linear_damping * linear_adjustment,
            self.stable_linear_damping * linear_adjustment,
            self.stable_linear_damping * linear_adjustment * 0.8  # Z轴稍小
        ])
        
        # 🎯 优化角阻尼分配，提高响应性
        angular_damping = np.array([
            self.stable_angular_damping * angular_adjustment * 1.2,  # 俯仰适中阻尼
            self.stable_angular_damping * angular_adjustment * 1.2,  # 横滚适中阻尼
            self.stable_angular_damping * angular_adjustment * 0.8   # 偏航更低阻尼
        ])
        
        # 调试信息
        debug_info = {
            'linear_speed': linear_speed,
            'angular_speed': angular_speed,
            'smooth_linear_speed': np.linalg.norm(smooth_linear_vel),
            'smooth_angular_speed': np.linalg.norm(smooth_angular_vel),
            'linear_adjustment': linear_adjustment,
            'angular_adjustment': angular_adjustment,
            'base_linear_damping': self.stable_linear_damping,
            'base_angular_damping': self.stable_angular_damping
        }
        
        return {
            'linear_damping': linear_damping,
            'angular_damping': angular_damping,
            'debug_info': debug_info
        }
    
    def get_damping_forces(self, linear_velocity: np.ndarray, 
                          angular_velocity: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        直接计算稳定的阻尼力和力矩
        
        Returns:
            Tuple[linear_damping_force, angular_damping_torque]
        """
        damping_result = self.calculate_stable_damping(linear_velocity, angular_velocity)
        
        # 计算阻尼力 F = -c * v
        linear_damping_force = -damping_result['linear_damping'] * linear_velocity
        
        # 计算阻尼力矩 T = -c * ω
        angular_damping_torque = -damping_result['angular_damping'] * angular_velocity
        
        return linear_damping_force, angular_damping_torque
    
    def get_system_status(self) -> Dict:
        """获取系统状态"""
        status = {
            'system_type': 'stable_damping',
            'critical_linear_damping': self.critical_damping_linear,
            'stable_linear_damping': self.stable_linear_damping,
            'critical_angular_damping': self.critical_damping_angular,
            'stable_angular_damping': self.stable_angular_damping,
            'history_length': len(self.velocity_history),
            'smooth_config': self.smooth_config
        }
        
        if self.velocity_history:
            status.update({
                'current_linear_speed': np.linalg.norm(self.velocity_history[-1]),
                'current_angular_speed': np.linalg.norm(self.angular_velocity_history[-1])
            })
        
        return status


def create_stable_damping_system(mass: float, inertia: list,
                               characteristic_length: float,
                               fast_angular_response: bool = True) -> StableDampingSystem:
    """
    创建稳定阻尼系统的便捷函数

    Args:
        mass: 质量 (kg)
        inertia: 惯性矩 [Ixx, Iyy, Izz] (kg·m²)
        characteristic_length: 特征长度 (m)
        fast_angular_response: 是否启用快速角度响应模式

    Returns:
        StableDampingSystem 实例
    """
    system = StableDampingSystem(mass, inertia, characteristic_length)

    if fast_angular_response:
        # 🎯 快速角度响应模式配置
        fast_config = {
            'angular_smoothing_factor': 0.3,     # 更快的角速度响应
            'angular_adjustment_rate': 0.25,     # 更快的角阻尼调整
            'angular_max_multiplier': 1.5,       # 适中的最大倍数
            'angular_min_multiplier': 0.3,       # 更小的最小倍数
            'angular_velocity_threshold': 0.02,  # 更低的角速度阈值
        }
        system.smooth_config.update(fast_config)
        print("🎯 快速角度响应模式已启用")

    return system
